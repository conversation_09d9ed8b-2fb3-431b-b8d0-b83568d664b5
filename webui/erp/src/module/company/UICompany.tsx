import React from 'react';

import { app, util, bs, input, entity } from '@datatp-ui/lib';

import { T } from "./Dependency";

import { UIContactForm } from '../../module/resource';
import { UIBankAccountList, UIBankAccountListPlugin } from '../../module/resource/UIBankAccountList';
import { BBRefCompany } from './BBRefCompany';
import { BBRefAccount } from "../../module/account";
import { UILoadableCompanyConfig } from './config/UICompanyConfig';

const SESSION = app.host.DATATP_HOST.session;

export class UICompanyForm extends entity.AppDbEntity {
  onGenerateCode(label: any) {
    const { toFileName } = util.text;
    let { observer } = this.props;
    if (!observer.isNewBean()) return;
    let code = entity.UUIDTool.generateWithAnyTokens(
      T("You need to enter the label"),
      [toFileName(label)],
      false
    );
    observer.getMutableBean().code = code;
    this.forceUpdate();
  }

  render() {
    let { appContext, pageContext, observer } = this.props;
    let bean = observer.getMutableBean();
    const adminCap = pageContext.hasUserAdminCapability();
    return (
      <div className='flex-vbox p-1'>
        <bs.Row>
          <bs.Col span={8}>
            <input.BBStringField
              bean={bean} field={'label'} label={T('Label')} disable={!adminCap} required
              onInputChange={(bean, field, oldVal, newVal) => this.onGenerateCode(newVal)} inputObserver={observer} />
          </bs.Col>
          <bs.Col span={4}>
            <input.BBStringField
              bean={bean} field={'code'} label={T('Code')} disable={!adminCap || !observer.isNewBean()} required
              onRefresh={() => this.onGenerateCode(bean.label)} inputObserver={observer} />
          </bs.Col>
        </bs.Row>
        <input.BBStringField
          bean={bean} field={'fullName'} label={T('FullName')} disable={!adminCap} />
        <BBRefCompany
          appContext={appContext} pageContext={pageContext} disable={!adminCap}
          label={T('Parent Company')} placeholder='Enter Parent Company'
          bean={bean} companyIdField={'parentId'} companyLabelField={'parentLabel'} />
        <BBRefAccount
          appContext={appContext} pageContext={pageContext} disable={!adminCap}
          label={T('Admin')} placeholder='Enter An Account'
          bean={bean} accountIdField={'adminAccountId'} accountLabelField={'adminAccountLabel'} />
        <bs.Row>
          <bs.Col span={6}>
            <input.BBDateTimeField inputObserver={observer}
              bean={bean} field={"foundingDate"} label={T('Founding Date')}
              dateFormat={"DD/MM/YYYY"} timeFormat={false} disable={!adminCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBDateTimeField inputObserver={observer}
              bean={bean} field={"closingDate"} label={T('Closing Date')}
              dateFormat={"DD/MM/YYYY"} timeFormat={false} disable={!adminCap} />
          </bs.Col>
        </bs.Row>
        <input.BBStringField
          bean={bean} field={'registrationCode'} label={T('Registration Code')} disable={!adminCap} />
        <input.BBTextField
          bean={bean} field={'description'} label={T('Description')}
          disable={!adminCap} style={{ height: '20em' }} />
      </div>
    );
  }
}

export class UINewCompanyEditor extends entity.AppDbEntityEditor {
  render() {
    let { appContext, pageContext, observer } = this.props;
    return (
      <div className='flex-vbox'>
        <UICompanyForm observer={observer} appContext={appContext} pageContext={pageContext} />
        <bs.Toolbar className='border'>
          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext} observer={observer}
            commit={{
              entityLabel: 'Company',
              context: 'system', service: 'CompanyService', commitMethod: 'createCompany'
            }}
            onPostCommit={this.onPostCommit} />
        </bs.Toolbar>
      </div>
    )
  }
}

export class UICompanyEditor extends entity.AppDbEntityEditor {
  render() {
    let { appContext, pageContext, observer, onPostCommit } = this.props;
    let modCap = pageContext.hasUserModeratorCapability();
    let bean = observer.getMutableBean();
    return (
      <bs.VSplit key={this.viewId} updateOnResize smallScreenView='tabs'>
        <bs.VSplitPane className='flex-vbox justify-content-between' title='Company' width={420}>
          <bs.Card header={T("Info")}>
            <UICompanyForm observer={observer} appContext={appContext} pageContext={pageContext} />
          </bs.Card>
          <bs.Toolbar className='border' hide={!modCap}>
            <entity.WButtonEntityReset appContext={appContext} pageContext={pageContext} observer={observer} />
            <entity.ButtonEntityCommit
              appContext={appContext} pageContext={pageContext} observer={observer}
              commit={{ entityLabel: 'Company', context: 'system', service: 'CompanyService', commitMethod: 'saveCompany' }}
              onPostCommit={onPostCommit} />
          </bs.Toolbar>
        </bs.VSplitPane>
        <bs.VSplitPane title='Others'>
          <bs.TabPane>
            <bs.Tab name="company-config" label={T('Config')} active={true} >
              <UILoadableCompanyConfig appContext={appContext} pageContext={pageContext} entityId={bean.id} />
            </bs.Tab>
            <bs.Tab name="company-info" label={T('Contact')} >
              <UILoadableCompanyInfo
                appContext={appContext} pageContext={pageContext}
                backend={{ context: 'system', service: 'CompanyService', loadMethod: 'getCompanyInfo', entityId: bean.code }} />
            </bs.Tab>
            <bs.Tab name="companyBankAccounts" label={T("Bank Accounts")}>
              <UIBankAccountList
                appContext={appContext} pageContext={pageContext}
                plugin={new UIBankAccountListPlugin('Company', bean.id, bean.code)}
                multiSelect={true} />
            </bs.Tab>
          </bs.TabPane>
        </bs.VSplitPane>
      </bs.VSplit >
    );
  }
}

export class UILoadableCompany extends entity.UILoadableEntity {
  createDefaultBackendConfig() {
    let companyACL = SESSION.getCurrentCompanyContext();
    const companyId = companyACL.companyId;
    let backend: entity.EntityBackendConfig = {
      context: 'system',
      service: 'CompanyService',
      loadMethod: 'loadCompanyById',
      entityId: companyId
    };
    return backend;
  }

  renderEntity(company: any) {
    const { appContext, pageContext } = this.props;
    let observer = new entity.ComplexBeanObserver(company);
    let html = (<UICompanyEditor appContext={appContext} pageContext={pageContext} observer={observer} />);
    return html;
  }
}

class UICompanyInfoEditor extends UIContactForm {
  render() {
    let { observer, appContext, pageContext } = this.props;
    let contact = observer.getMutableBean();
    let writeCap = pageContext.hasUserWriteCapability();
    return (
      <div className='flex-vbox'>
        <bs.ScrollableCards className='flex-vbox' style={{ minHeight: 400 }}>
          <bs.Card header={T("General")}>
            {this.renderAddress()}
            <input.BBStringField bean={contact} field={'taxCode'} label={T('Tax Code')} disable={!writeCap} />
          </bs.Card>
          <bs.Card header={T("Contacts")}>
            {this.renderContact()}
          </bs.Card>
        </bs.ScrollableCards>
        <bs.Toolbar className='border' hide={!writeCap}>
          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext} observer={observer}
            commit={{ entityLabel: 'Company Info', context: 'system', service: 'CompanyService', commitMethod: 'saveCompanyInfo' }} />
        </bs.Toolbar>
      </div>
    );
  }
}

export class UILoadableCompanyInfo extends entity.UILoadableEntity {
  protected createNewEntityBean() {
    let companyInfo = {
      code: this.backend.entityId
    };
    return companyInfo;
  }

  renderEntity(companyInfo: any) {
    const { appContext, pageContext } = this.props;
    let observer = new entity.ComplexBeanObserver(companyInfo);
    let html = (<UICompanyInfoEditor appContext={appContext} pageContext={pageContext} observer={observer} />);
    return html;
  }
}