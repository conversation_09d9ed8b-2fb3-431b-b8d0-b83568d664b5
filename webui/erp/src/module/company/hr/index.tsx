import * as common from '../../common';
import { ProjectPluginManager } from 'module/project'
import { TimeOffProjectPlugin } from './time-off/project/TimeOffProjectPlugin'
import { UIEmployeeLinkPlugin } from './employee/UIEmployeeLinkPlugin';

export * from './utilities'
export * from './employee/BBRefEmployee'
export * from './employee/BBRefMultiEmployee'
export * from './employee/BBRefDepartment'
export * from './employee/UIEmployeeListEditor'
export * from './employee/UIEmployeeList'
export * from './employee/UIEmployee'
export * from './UILoginPermissionList'
export * from './RestURL';
export * from './misc/BBRefWorkPosition'

export { UIEmployeeLinkPlugin } from './employee/UIEmployeeLinkPlugin'

export * from './time-off/BBRefTimeOffType'
export { UITimeOffList, UITimeOffListPlugin } from './time-off/UITimeOffList'

export { TimeOffProjectPlugin } from './time-off/project/TimeOffProjectPlugin'

export { UIPayslipAdjustmentList, UIPayslipAdjustmentListPlugin } from './timesheet/UIPayslipAdjustmentList'

export { init } from './init'

ProjectPluginManager.register(new TimeOffProjectPlugin());


common.LINK_PLUGIN_REGISTRY.register(new UIEmployeeLinkPlugin());
