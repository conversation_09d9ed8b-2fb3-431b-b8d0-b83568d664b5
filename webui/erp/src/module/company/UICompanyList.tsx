import React, { Component } from 'react';

import { util, grid, sql, bs, app, entity } from '@datatp-ui/lib';

import { T } from './Dependency';

import { UICompanyEditor, UINewCompanyEditor } from './UICompany';

export class UICompanyListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);
    this.backend = { context: 'company', service: 'CompanyService', searchMethod: 'searchCompanies' }

    this.searchParams = {
      "filters": [
        ...sql.createSearchFilter(),
      ],
      "orderBy": {
        fields: ["label", "code", "modifiedTime"],
        fieldLabels: [T("Label"), T('Code'), T("Modified Time")],
        selectFields: ["modifiedTime"],
        sort: "DESC"
      },
      "maxReturn": 1000
    }
  }
  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }
}

export class UICompanyList extends entity.DbEntityList<entity.DbEntityListProps> {
  createVGridConfig() {
    let { type } = this.props;
    let config: grid.VGridConfig = {
      title: T("Companies"),
      record: {
        fields: [
          // ...DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', T('Label'), 350),

          { name: 'code', label: T('Code'), width: 150, state: { visible: false } },
          { name: 'fullName', label: T('FullName'), width: 450 },
          { name: 'registrationCode', label: T('Registration Code'), width: 150, state: { visible: false } },
          { name: 'adminAccountLabel', label: T('Admin'), width: 120, state: { visible: false } },
          {
            name: 'foundingDate', label: T('Founding Date'), width: 120,
            format: util.text.formater.compactDate, state: { visible: false }
          },
          {
            name: 'closingDate', label: T('Closing Date'), width: 120,
            format: util.text.formater.compactDate, state: { visible: false }
          },
          { name: 'parentLabel', label: T('Parent'), width: 150, state: { visible: false } },
          { name: 'description', label: T('Description'), width: 400 },
          { name: 'id', label: T('Company Id'), state: { visible: false } },
          ...entity.DbEntityListConfigTool.FIELD_ENTITY
        ]
      },
      toolbar: {
        actions: [
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true)
      },

      footer: {
        page: {
          hide: type !== 'page',
          render: (ctx: grid.VGridContext) => {
            return (<UICompanyListPageControl context={ctx} />);
          }
        },
      },

      view: {
        currentViewName: 'tree',
        availables: {
          tree: {
            viewMode: 'tree',
            treeField: 'label',
            plugin: new grid.TreeDisplayModelPlugin()
          },
          aggregation: {
            viewMode: 'aggregation',
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel(T('All'), false);
              return model;
            }
          }
        },
      },
    }
    return config;
  }

  onDefaultSelect(dbRecord: grid.DisplayRecord) {
    let bean = dbRecord.record;
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UICompanyEditor
          appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver(bean)}
          onPostCommit={this.onAddOrModifyDBRecordCallback} />)
    }
    pageContext.createPopupPage('company', T('Company'), createAppPage, { size: 'xl', backdrop: 'static' });
  }
}

class UICompanyListPageControl extends Component<grid.VGridContextProps> {
  onNew() {
    let { context } = this.props;
    let uiCompanyList = context.uiRoot as UICompanyList;
    let { pageContext } = uiCompanyList.props;
    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UINewCompanyEditor
          appContext={appCtx} pageContext={pageCtx}
          observer={new entity.ComplexBeanObserver({})} onPostCommit={uiCompanyList.onAddOrModifyDBRecordCallback} />
      )
    }
    pageContext.createPopupPage("new-company", T('New Company'), createPageContent);
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as app.AppComponent;
    let { appContext, pageContext } = uiRoot.props;
    let adminCap = pageContext.hasUserAdminCapability();
    let html = (
      <bs.Toolbar className='border' hide={!adminCap}>
        <entity.WButtonEntityNew
          appContext={appContext} pageContext={pageContext}
          label={T('New Company')} onClick={() => this.onNew()} hide={!adminCap} />
      </bs.Toolbar>
    );
    return html;
  }
}