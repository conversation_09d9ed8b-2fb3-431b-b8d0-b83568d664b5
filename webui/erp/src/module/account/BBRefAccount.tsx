import React from 'react';
import { entity, sql } from '@datatp-ui/lib';


interface BBRefAccountProps extends entity.BBRefEntityProps {
  accountIdField: string;
  accountLabelField: string;
  accountType?: 'USER' | 'ORGANIZATION' //TODO: Dan - ('USER' | 'ORGANIZATION')[]; => must be update to array, query in backend
}

function createConfig(params: BBRefAccountProps) {
  const { accountIdField, accountLabelField, accountType } = params;

  let config: entity.BBRefEntityPluginConfig = {
    backend: {
      context: 'system',
      service: 'AccountService',
      searchMethod: 'searchAccounts',
      loadMethod: 'getReadOnlyAccountById',

      createSearchParams: (searchParams: sql.SqlSearchParams, _userInput: string) => {
        searchParams.maxReturn = 1000
        searchParams.params = {}
        searchParams.optionFilters = [
          {
            "name": "accountType", "label": "Account Type", "type": "STRING", "required": true,
            "options": ["", "USER", "ORGANIZATION"],
            "optionLabels": ["All", "User", "Organization"],
            "selectOption": accountType || 'USER'
          }
        ]
        return searchParams
      },

    },
    bean: {
      idField: accountIdField,
      labelField: accountLabelField,
      mapSelect: (ui: entity.BBRefEntity, bean: any, selectOpt: any, idValue: any, labelValue: any) => {
        if (selectOpt) {
          bean[accountIdField] = idValue;
          bean[accountLabelField] = labelValue;
          return 'success';
        } else {
          bean[accountIdField] = null;
          bean[accountLabelField] = null;
          let { allowUserInput } = ui.props;
          if (allowUserInput) {
            bean[accountLabelField] = labelValue;
            return 'success';
          } else {
            return 'fail';
          }
        }
      },
      renderRefEntityInfoTooltip: (bean: any) => {
        let html = (
          <div className='flex-vbox-grow-0'>
            {bean[accountLabelField]}({bean[accountIdField]})
          </div>
        );
        return html;
      }
    },
    refEntity: {
      idField: 'id',
      labelField: 'fullName',
      labelFunc: (opt: any) => {
        return `${opt['loginId']} / ${opt['fullName']}`
      },

      vgridRecordConfig: {
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('fullName', 'Full Name', 350),
          { name: 'loginId', label: 'Login Id', width: 150 },
          { name: 'accountType', label: 'Account Type', width: 150 },
          { name: 'email', label: 'Email', width: 250 },
          { name: 'mobile', label: 'Mobile', width: 150 }
        ]
      }
    },
  };
  return config;
}

export class BBRefAccount extends entity.BBRefEntity<BBRefAccountProps> {

  createPlugin() {
    let config = createConfig(this.props);
    return new entity.BBRefEntityPlugin(config);
  }

}

export class BBRefMultiAccount extends entity.BBRefMultiEntity<BBRefAccountProps> {
  createPlugin() {
    let config = createConfig(this.props);
    return new entity.BBRefEntityPlugin(config);
  }
}
