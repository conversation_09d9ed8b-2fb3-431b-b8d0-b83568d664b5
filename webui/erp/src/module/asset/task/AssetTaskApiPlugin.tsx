import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather'
import { util, server, bs, app, entity, input, grid } from '@datatp-ui/lib';
import { ApiResponse, UIApiPlugin } from 'module/security';
import { T } from '../Dependency';
import { AssetTaskStatus, AssetTaskType } from '../models';

function apiResourceCall(authorization: string, params: any, callback: (resp: server.BackendResponse) => void) {
  const CONFIG = app.host.CONFIG
  let restClient = new server.rest.RestClient(CONFIG.getServerUrl(), CONFIG.getApiUrl(), 'datatp');
  let header = {
    'DataTP-Authorization': authorization
  }
  restClient.postWithHeader(`/resource`, header, params, callback)
}

export class UIApiPluginAssetTask extends UIApiPlugin {
  constructor() {
    super('resource:asset-task-confirmation', 'Asset Task Confirmation');
  }

  render(appContext: app.AppContext, pageContext: app.PageContext, resp: ApiResponse) {
    let html = (
      <div className='content flex-vbox p-0'>
        <UIAssetTaskConfirmation
          appContext={appContext} pageContext={pageContext} authorization={resp.authorization} data={resp.result}
          allowedResourceIds={resp.allowedResourceIds} />
      </div>
    )
    return html;
  }
}

interface UIAssetTaskConfirmationProps extends app.AppComponentProps {
  authorization: any;
  data: any;
  allowedResourceIds: any;
}
export class UIAssetTaskConfirmation extends app.AppComponent<UIAssetTaskConfirmationProps> {

  onChangeStatus = (status: 'Approved' | 'Rejected', assetId?: number) => {
    setTimeout(() => {
      window.close();
    }, 1500);

    let { appContext, authorization, data, allowedResourceIds } = this.props;
    let taskableAsset = data['taskableAsset']
    let receiverAccountId = allowedResourceIds['receiverAccountId'];
    let params = {
      taskableAssetId: taskableAsset['id'],
      status: status,
      assetId: assetId,
      receiverAccountId: receiverAccountId
    }

    apiResourceCall(authorization, params, (resp: server.BackendResponse) => {
      let taskableAsset = resp.data.result['taskableAsset'];
      if (taskableAsset) {
        if (taskableAsset['status'] === status) {
          appContext.addOSNotification('success', T(`Task ${status}!`));
          setTimeout(() => {
            window.close();
          }, 2000);
        } else {
          appContext.addOSNotification('danger', T(`Task ${taskableAsset['status']} Already!`));
        }
      }
    });
  }

  formatDateTime(dateStr: string) {
    return {
      date: dateStr.substring(0, 10),
      time: dateStr.substring(11, 16)
    };
  }

  renderAvailableAssets = () => {
    let { data } = this.props;
    let htmls: any[] = [];
    let availableAssets: any[any] = data['availableAssets'];
    if (!availableAssets || availableAssets.length < 1) {
      htmls.push(
        <div className='flex-hbox justify-content-start '>
          <span>- If agree click</span>
          <button type='button' className='btn btn-link mx-1 p-0' onClick={() => this.onChangeStatus('Approved')}>
            <FeatherIcon.CheckCircle size={16} /> Approve
          </button>
          <span> else click </span>
          <button type='button' className='btn btn-link text-danger mx-1 p-0' onClick={() => this.onChangeStatus('Rejected')}>
            <FeatherIcon.XCircle size={16} /> Reject
          </button>
        </div>
      );
    } else {
      for (let asset of availableAssets) {
        htmls.push(
          <div key={asset['id']} className='flex-hbox justify-content-start '>
            <span>- If agree to</span>
            <span className='mx-1 text-primary fw-bold'>{asset['label']}</span>
            <span>click</span>
            <button type='button' className='btn btn-link mx-1 p-0' onClick={() => this.onChangeStatus('Approved', asset['id'])}>
              <FeatherIcon.CheckCircle size={16} /> Approve
            </button>
            <span> else click </span>
            <button type='button' className='btn btn-link text-danger mx-1 p-0' onClick={() => this.onChangeStatus('Rejected')}>
              <FeatherIcon.XCircle size={16} /> Reject
            </button>
          </div>
        );
      }
    }
    return (
      <div>
        {htmls}
      </div>
    )
  }

  renderTasksOnUsingDate = () => {
    let { data } = this.props;
    let htmls: any[] = [];
    let taskableAsset = data['taskableAsset'];
    let tasksOnUsingDate: any[any] = data['tasksOnUsingDate'];
    if (!tasksOnUsingDate || tasksOnUsingDate.length < 1) return <></>;

    for (let task of tasksOnUsingDate) {
      let currentStatus: 'Pending' | 'Approved' | 'Rejected' = task['status'];
      if (task.id === taskableAsset['id'] || currentStatus === AssetTaskStatus.Rejected.value) continue;

      htmls.push(
        <table className="table table-borderless">
          <tbody>
            <tr>
              <td className='py-1' width={180}>- {task['taskType']} requirement date</td>
              <td className='py-1'>: {this.formatDateTime(task['usingDate']).date}</td>
            </tr>
            <tr>
              <td className='py-1 ps-3' width={180}>+ From</td>
              <td className='py-1' >: {this.formatDateTime(task['fromTime']).time}</td>
            </tr>
            <tr>
              <td className='py-1 ps-3' width={180}>+ To</td>
              <td className='py-1' >: {this.formatDateTime(task['toTime']).time}</td>
            </tr>
            <tr>
              <td className='py-1 ps-3' width={180}>+ Contents</td>
              <td className='py-1' >: {task['label']}</td>
            </tr>
            <tr>
              <td className='py-1 ps-3' width={180}>+ Requested by</td>
              <td className='py-1' >: <span className='fw-bold'>{task['picLabel']}</span></td>
            </tr>
            <tr>
              <td className='py-1 ps-3' width={180}>+ Start place</td>
              <td className='py-1' >: {task['startPlace']}</td>
            </tr>
            <tr>
              <td className='py-1 ps-3' width={180}>+ End place</td>
              <td className='py-1' >: {task['endPlace']}</td>
            </tr>
            <tr>
              <td className='py-1 ps-3' width={180}>+ {task['taskType']}</td>
              <td className='py-1' >: <span className='fw-bold'>{task['assetLabel']}</span></td>
            </tr>
            <tr>
              <td className='py-1 ps-3' width={180}>+ Notes</td>
              <td className='py-1' >: {task['description']}</td>
            </tr>
            <tr>
              <td className='py-1 ps-3' width={180}>+ Created by</td>
              <td className='py-1' >: {task['createdByAccountFullName']}</td>
            </tr>
            <tr>
              <td className='py-1 ps-3' width={180}>+ Current status</td>
              <td className='py-1' >: <span className={`text-${AssetTaskStatus[currentStatus].color}`}>{currentStatus}</span></td>
            </tr>
          </tbody>
        </table>
      );
    }

    return (
      <div className='mt-2'>
        <u><i>Current schedule list as below:</i></u>
        {htmls}
      </div>
    )
  }

  render() {
    let { data } = this.props;
    if (!data) {
      return (
        <div className='flex-vbox align-items-center justify-content-center'>
          <div className='align-item-center justify-content-center border rounded p-3 m-1' style={{ width: 800 }}>
            <h4 className='text-center text-primary mb-3'>Could Not Found Request!</h4>
            <h3 className='text-center text-bold text-warning'>Request might be deleted or archived!</h3>
            <h5 className='text-center'>Go to <a href='https://beelogistics.cloud'>{'Bee Logistics Cloud'}</a></h5>
          </div>
        </div>
      )
    }
    let taskableAsset = data['taskableAsset'];
    let currentStatus: 'Pending' | 'Approved' | 'Rejected' = taskableAsset['status'];
    if (currentStatus !== AssetTaskStatus.Pending.value) {
      return (
        <div className='flex-vbox align-items-center justify-content-center'>
          <div className='align-item-center justify-content-center border rounded p-3 m-1' style={{ width: 800 }}>
            <h4 className='text-center text-primary mb-3'>Approve {taskableAsset['taskType']} Request</h4>
            <h3 className='text-center text-bold text-warning'>The requirement has {currentStatus}!</h3>
          </div>
        </div>
      )
    }
    return (
      <div className='flex-vbox align-items-center justify-content-center'>
        <div className='align-item-center justify-content-center border rounded p-3 m-1' style={{ width: 800 }}>
          <h4 className='text-center text-primary mb-3'>Approve {taskableAsset['taskType']} Request</h4>
          <i>You have new {taskableAsset['taskType']} requirement alert as follows:</i>

          <table className="table table-borderless">
            <tbody>
              <tr>
                <td className='py-1' width={180}>- {taskableAsset['taskType']} requirement date</td>
                <td className='py-1'>: <span className='fw-bold'>{this.formatDateTime(taskableAsset['usingDate']).date}</span></td>
              </tr>
              <tr>
                <td className='py-1' width={180}>- From</td>
                <td className='py-1' >: <span className='fw-bold'>{this.formatDateTime(taskableAsset['fromTime']).time}</span></td>
              </tr>
              <tr>
                <td className='py-1' width={180}>- To</td>
                <td className='py-1' >: <span className='fw-bold'>{this.formatDateTime(taskableAsset['toTime']).time}</span></td>
              </tr>
              <tr>
                <td className='py-1' width={180}>- Contents</td>
                <td className='py-1' >: <span className='fw-bold'>{taskableAsset['label']}</span></td>
              </tr>
              <tr>
                <td className='py-1' width={180}>- Requested by</td>
                <td className='py-1' >: <span className='fw-bold'>{taskableAsset['picLabel']}</span></td>
              </tr>
              <tr>
                <td className='py-1' width={180}>- Start place</td>
                <td className='py-1' >: <span className='fw-bold'>{taskableAsset['startPlace']}</span></td>
              </tr>
              <tr>
                <td className='py-1' width={180}>- End place</td>
                <td className='py-1' >: <span className='fw-bold'>{taskableAsset['endPlace']}</span></td>
              </tr>
              <tr>
                <td className='py-1' width={180}>- {taskableAsset['taskType']}</td>
                <td className='py-1' >:  <span className='fw-bold'>{taskableAsset['assetLabel']}</span></td>
              </tr>
              <tr>
                <td className='py-1' width={180}>- Notes</td>
                <td className='py-1' >: <span className='fw-bold'>{taskableAsset['description']}</span></td>
              </tr>
              <tr>
                <td className='py-1' width={180}>- Created by</td>
                <td className='py-1' >: <span className='fw-bold'>{taskableAsset['createdByAccountFullName']}</span></td>
              </tr>
              <tr>
                <td className='py-1' width={180}>- Current status</td>
                <td className='py-1' >: <span className={`text-${AssetTaskStatus[currentStatus].color}`}>{currentStatus}</span></td>
              </tr>
            </tbody>
          </table>
          {this.renderAvailableAssets()}
          {this.renderTasksOnUsingDate()}
        </div>
      </div >
    );
  }
}

