import React from 'react';
import { grid, util, input, bs } from '@datatp-ui/lib';
import { DTablePermissionsHelper } from "./DTablePermissionHelper";

type Capability = "None" | "Read" | "Write" | "Moderator" | "Admin";
export type CellType = "String" | "Integer" | "Double" | "Boolean" | "Date";
export type CellFormat = "None" | "Currency" | "Decimal" | "Capital" | "Date" | "Datetime";
export type AutocompleteType = "None" | "LoginId" | "Spreadsheet" | "Metadata";
type SummaryCellMode = "None" | "Label" | "Desc" | "Info";

export interface DTablePermission {
  userId: number;
  loginId: string;
  label: string;
  capability: Capability;
  type: "Employee" | "Partner";
}

export interface DTablePluginConfig {
  name: string;
  label: string;
  minPluginAccessCapability: Capability;
  pluginType: string;
}

export interface JSMacro {
  name: string;
  label: string;
  macro: string;
}

export interface DtableColumnGroup {
  idx: number;
  name: string;
  label: string;
  visible: boolean;
  minColumnAccessCapability: Capability;
  columns: DTableColumn[];
}

export interface DTableColumn {
  idx: number;
  column_group_id: number;
  columnId: string;
  refColumnId: string;
  refDTableCode: string;
  label: string;
  width: number;
  type: CellType;
  format: CellFormat;
  options: string[];
  groupBy: boolean;
  sortable: boolean;
  sortPriority?: number;
  sortDirection?: "ASC" | "DESC";
  allowUserInput: boolean;
  filterable: boolean;
  searchable: boolean;
  container: 'fixed-left' | 'default' | "fixed-right";
  showState: boolean;
  autoCompleteType: AutocompleteType;
  summaryCellMode: SummaryCellMode;
  description: string;
}

export interface DTableConfig {
  id: number;
  code: string;
  label: string;
  ownerAccountId: number;
  ownerLabel: string;
  summarySupport: boolean;
  summaryCellHeight: number;
  minRowAccessCapability: Capability;
  columnGroups: Array<DtableColumnGroup>;
  jsMacros: Array<JSMacro>;
  jsPlugins: Array<string>;
  plugins: Array<DTablePluginConfig>;
  permissions: Array<DTablePermission>;
}

export interface SummaryModel {
  isSupported: boolean;
  cellHeight: number;
  fieldLabel: string;
  fieldDesc: string;
  fieldInfo: string[];
}

export interface RefFieldModel {
  refFieldName: string;
  fieldName: string;
}

const uiEditBBSelectField = (
  field: grid.FieldConfig, dRecord: grid.DisplayRecord, tabIndex: number, focus: boolean,
  onInputChange: grid.OnInputChange, options: Array<any>, className: string) => {
  let bean = dRecord.record;
  return (
    <input.BBSelectField bean={bean} field={field.name} options={options} onInputChange={onInputChange}
      tabIndex={tabIndex} focus={focus} className={className} />
  );
}

export class DTable {
  _spreadsheet: DTableConfig;
  _permissionHelper: DTablePermissionsHelper;
  _searchableColumnMap: Map<CellType, Array<DTableColumn>> = new Map<CellType, any>();
  _supportedAutoCompleteColumnMap: Map<string, AutocompleteType> = new Map<string, AutocompleteType>();
  _summaryModel: SummaryModel;
  _fields: Array<grid.FieldConfig> = [];
  _fieldGroups: Record<string, grid.FieldGroup> = {};
  _sortableColumns: Array<DTableColumn> = [];
  _columns: Record<string, DTableColumn> = {};

  //TODO: Dan - remove this variable
  _autoCompleteOptionMap: Map<string, any[]> = new Map<string, any[]>();
  _autoCompleteFieldMap: Map<string, RefFieldModel[]> = new Map<string, RefFieldModel[]>();

  //when group by, calculate total columns has type number be default
  _numberedColumns: Array<string> = new Array<string>();
  _groupByColumns: Array<DTableColumn> = new Array<DTableColumn>();

  constructor(context: grid.VGridContext, config: DTableConfig, accessAccountId: number) {
    this._spreadsheet = config;
    this._permissionHelper = new DTablePermissionsHelper(accessAccountId, config);

    this._fields.push({ name: 'ownerLabel', label: 'Owner', width: 300, filterable: true, filterableType: 'options', container: 'fixed-left' });

    this._summaryModel = {
      isSupported: this._spreadsheet.summarySupport,
      cellHeight: this._spreadsheet.summaryCellHeight,
      fieldDesc: '',
      fieldLabel: '',
      fieldInfo: []
    }

    for (let columnGroup of this._spreadsheet.columnGroups) {
      this.buildFieldGroup(columnGroup);
      for (let column of columnGroup.columns) {
        this.buildField(context, columnGroup, column);
        let columnId = column.columnId;
        let type = column.type;
        this._columns[column.columnId] = column;
        // detect sort column
        if (column.sortable) this._sortableColumns.push(column);

        if (type === "Double" || type === "Integer") this._numberedColumns.push(columnId);

        let autoCompleteType: AutocompleteType = column.autoCompleteType;
        if (!autoCompleteType) autoCompleteType = 'None';
        if (autoCompleteType != 'None') {
          this._supportedAutoCompleteColumnMap.set(columnId, autoCompleteType);
          this._autoCompleteOptionMap.set(columnId, []);
        }
        if (columnId.includes(':')) {
          let array = columnId.split(":");
          let spreadsheetCode = array[0];
          let refColumnId = array[1];
          if (this._autoCompleteFieldMap.has(spreadsheetCode)) {
            this._autoCompleteFieldMap.get(spreadsheetCode)?.push({ refFieldName: refColumnId, fieldName: columnId });
          } else {
            this._autoCompleteFieldMap.set(spreadsheetCode, [{ refFieldName: refColumnId, fieldName: columnId }]);
          }
        }

        let refColumnId = column.refColumnId;
        let refSpreadSheetCode = column.refDTableCode;
        if (refColumnId && refSpreadSheetCode) {
          if (this._autoCompleteFieldMap.has(refSpreadSheetCode)) {
            this._autoCompleteFieldMap.get(refSpreadSheetCode)?.push({ refFieldName: refColumnId, fieldName: columnId });
          } else {
            this._autoCompleteFieldMap.set(refSpreadSheetCode, [{ refFieldName: refColumnId, fieldName: columnId }]);
          }
        }

        let summaryCellMode = column.summaryCellMode;
        if ("Label" == summaryCellMode) {
          this._summaryModel.fieldLabel = columnId;
        } else if ("Desc" == summaryCellMode) {
          this._summaryModel.fieldDesc = columnId;
        } else if ("Info" == summaryCellMode) {
          this._summaryModel.fieldInfo?.push(columnId);
        }

        if (column.searchable) {
          let holder = this._searchableColumnMap.get(type) as Array<any>;
          if (!holder) {
            holder = new Array<any>();
            this._searchableColumnMap.set(type, holder);
          }
          holder.push(column);
        }
        if (column.groupBy) this._groupByColumns.push(column);
      }
    }
  }

  buildFieldGroup(columnGroup: DtableColumnGroup) {
    let fields = columnGroup.columns.map(sel => sel.columnId);
    let fieldGroup: grid.FieldGroup = { label: columnGroup.label, fields, visible: columnGroup.visible };
    if (!columnGroup.label || columnGroup.label.trim().length == 0) {
      this._fieldGroups["-"] = fieldGroup;
    } else {
      this._fieldGroups[columnGroup.label] = fieldGroup;
    }
  }

  buildField(context: grid.VGridContext, columnGroup: DtableColumnGroup, column: DTableColumn) {
    let columnId = column.columnId;
    let container = column.container;
    let visible = columnGroup.visible;
    let options = column.options;
    let type = column.type;
    let format = column.format;

    let fieldConfig: grid.FieldConfig = {
      name: columnId, label: column.label, width: column.width, hint: column.description,
      sortable: column.sortable, filterable: column.filterable, dataTooltip: true,
      dataType: type,
      editor: {
        type: column.type, enable: true,
        onInputChange(fieldCtx: grid.FieldContext, oldVal: any, newVal: any) {
          let { fieldConfig, displayRecord, gridContext } = fieldCtx;
          let event: grid.VGridCellEvent = {
            row: displayRecord.row, field: fieldConfig, event: 'Modified'
          }
          gridContext.broadcastCellEvent(event);
        },

      },
      state: { visible: visible, showRecordState: column.showState },
      filterableType: (options && options.length > 0) ? 'options' : column.type,
      container: container ? container : 'default',
    }

    if (!!column.showState) {
      fieldConfig.listener = {
        onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
          if (cell.getRow() == event.row) cell.forceUpdate();
        }
      }
    }

    if (fieldConfig.editor && fieldConfig.filterableType === 'options') {
      fieldConfig.editor.renderCustom = (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
        let { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
        let className = genClassName(context, displayRecord);
        return uiEditBBSelectField(fieldConfig, displayRecord, tabIndex, focus, onInputChange, options, className);
      }
    }

    let genClassName = (context: grid.VGridContext, dRecord: grid.DisplayRecord): string => {
      let className = fieldConfig.cssClass ? fieldConfig.cssClass : '';
      if (fieldConfig.computeCssClasses) {
        className = bs.mergeCssClass(className, fieldConfig.computeCssClasses(context, dRecord));
      }
      return className;
    }

    if (type === 'Date') {
      if (format === 'Date') {
        fieldConfig.format = util.text.formater.compactDate;
        if (fieldConfig.editor) fieldConfig.editor.renderCustom = (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
          let { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
          let className = genClassName(context, displayRecord);
          return <input.BBDateTimeField bean={displayRecord.record} field={fieldConfig.name} timeFormat={false} className={className}
            tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
        }
      } else if (format === 'Datetime') {
        fieldConfig.format = util.text.formater.compactDateTime;
        if (fieldConfig.editor) fieldConfig.editor.renderCustom = (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
          let { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
          let className = genClassName(context, displayRecord);
          return <input.BBDateTimeField bean={displayRecord.record} field={fieldConfig.name} timeFormat={true} className={className}
            tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
        }
      } else fieldConfig.format = util.text.formater.compactDate;
    }
    if (type === 'String' && format === 'Capital') {
      fieldConfig.style = { textTransform: 'uppercase' };
      if (fieldConfig.editor) fieldConfig.editor.renderCustom = (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
        let { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
        let className = genClassName(context, displayRecord);
        return <input.BBStringField bean={displayRecord.record} field={fieldConfig.name} style={{ textTransform: 'uppercase' }}
          tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} className={className} />
      }
    }
    if (type === 'Double' && format === 'Currency') {
      if (fieldConfig.editor) fieldConfig.editor.renderCustom = (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
        let { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
        let className = genClassName(context, displayRecord);
        return <input.BBNumberField bean={displayRecord.record} field={fieldConfig.name} maxPrecision={0} className={className}
          tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
      }
    }
    if (type === 'Double' && format === 'Decimal') {
      if (fieldConfig.editor) fieldConfig.editor.renderCustom = (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
        let { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
        let className = genClassName(context, displayRecord);
        return <input.BBNumberField bean={displayRecord.record} field={fieldConfig.name} maxPrecision={2} className={className}
          tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
      }
    }

    this._fields.push(fieldConfig);
  }

  getSearchableColumnMap(): Map<CellType, Array<DTableColumn>> {
    return this._searchableColumnMap;
  }

  getConfig(): DTableConfig {
    return this._spreadsheet;
  }

  getSortableColumns(): Array<DTableColumn> {
    return this._sortableColumns.sort((rec1, rec2) => {
      if (!rec1.sortPriority || !rec2.sortPriority) {
        //throw error
        return 0;
      }
      // descending order
      return rec2.sortPriority - rec1.sortPriority;
    })
  }

  findSpreadsheetColumnsByNames(columnNames: Array<string>): Array<DTableColumn> {
    let holder: Array<DTableColumn> = [];
    for (let columnName of columnNames) {
      let spreadsheetColumn = this._columns[columnName];
      if (!spreadsheetColumn) throw Error(`Column name ${columnName} not found!!!`);
      holder.push(spreadsheetColumn);
    }
    return holder;
  }

  getInitialSetupMacro(): string | undefined {
    return this._spreadsheet.jsMacros.find((m) => m.name.startsWith("initial-setup:"))?.macro;
  }

  getDefaultSortMacro(): string | undefined {
    return this._spreadsheet.jsMacros.find((m) => m.name == "default:sort")?.macro;
  }

  getPermissionHelper(): DTablePermissionsHelper {
    return this._permissionHelper;
  }

  getAccessColumns(): DTableColumn[] {
    return Array.from(this._permissionHelper.allowedAccessColumnMap.values());
  }

  hasSummarySupport(): boolean {
    return this._spreadsheet.summarySupport;
  }

  getNumberedColumns(): Array<string> {
    return this._numberedColumns;
  }

  getSummaryModel(): SummaryModel {
    return this._summaryModel;
  }

  getGroupByColumns(): Array<DTableColumn> {
    return this._groupByColumns;
  }
}
