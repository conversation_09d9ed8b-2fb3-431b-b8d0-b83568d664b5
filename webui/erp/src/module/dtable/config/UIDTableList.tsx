import React from 'react';
import * as FeatherIcon from 'react-feather';
import { app, server, grid, bs, sql, entity } from '@datatp-ui/lib';

import { T } from '../Dependency';
import { DTableRestURL } from '../RestURL';
import { UIDTableEditor } from './UIDTable';
import { UIDTableListControl } from './UIDTableListControl';
import { UIRowList } from '../data/UIRowList';

import { UIRowListPlugin } from '../data/UIRowListPlugin';
import { WAvatars } from 'module/account';

const SESSION = app.host.DATATP_HOST.session;

export class UIDTableListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      context: 'company',
      service: 'DTableService',
      searchMethod: 'searchDTables'
    }

    this.searchParams = {
      "params": {},
      "filters": [...sql.createSearchFilter()],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "orderBy": {
        fields: ["modifiedTime", "createdTime"],
        fieldLabels: [T("Modified Time"), T("Created Time")],
        selectFields: ["createdTime"],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call()
  }
}

export class UIDTableList extends entity.DbEntityList {
  createVGridConfig() {
    let { pageContext, type } = this.props
    let config: grid.VGridConfig = {
      record: {
        control: {
          width: 25,
          items: [
            {
              name: 'edit', hint: 'Edit', icon: FeatherIcon.Edit,
              customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let uiList = ctx.uiRoot as UIDTableList;
                let spreadsheet = dRecord.record;
                if (!pageContext.hasUserAdminCapability() && spreadsheet.ownerAccountId !== SESSION.getAccountId()) return;
                return (
                  <button className='btn btn-link p-0' onClick={() => uiList.onEditSpreadsheetConfig(dRecord)} >
                    <FeatherIcon.Edit size={12} />
                  </button>
                );
              },
            }
          ]
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', T('Label'), 350),
          { name: 'code', label: T('Code'), width: 200, state: { visible: false } },
          {
            name: "ownerLabel", label: T('Owner'), width: 170, cssClass: 'pe-1',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as UIDTableList
              const { appContext, pageContext } = uiList.props;
              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['ownerAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{record['ownerLabel']}</div>
                </div>
              )
            }
          },
          { name: 'minRowAccessCapability', label: T('Min Row Access Capability'), width: 250, state: { visible: false } },
          { name: 'description', label: T('Description'), width: 300 },
          ...entity.DbEntityListConfigTool.FIELD_ENTITY
        ],
        summary: {
          dataCellHeight: 60,
          render: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord, _viewMode: grid.ViewMode) => {
            if (dRecord.type == 'bucket') {
              return <></>;
            }
            if (dRecord.type == 'agg') {
              let html = (
                <div className='flex-hbox align-items-end justify-content-between'>
                  TODO...
                </div>
              );
              return html;
            }
            let html = (
              <grid.SummaryCell className='flex-hbox' context={ctx} record={dRecord}>
                <grid.SummaryInfo context={ctx} labelField='label'
                  descField='code' infoField={['ownerLabel', 'minRowAccessCapability']} record={dRecord} />
              </grid.SummaryCell>
            );
            return html;
          }
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!pageContext.hasUserAdminCapability(), T('Delete'))
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true)
      },
      footer: {
        page: {
          hide: type !== 'page',
          render: (ctx: grid.VGridContext) => {
            return (<UIDTableListControl context={ctx} />);
          }
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT("Select Type", type)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }

  onEditSpreadsheetConfig(dRecord: grid.DisplayRecord) {
    let { appContext, pageContext } = this.props;
    let spreadsheet = dRecord.record;
    if (!pageContext.hasUserAdminCapability() && spreadsheet.ownerAccountId !== SESSION.getAccountId()) return;

    appContext.createHttpBackendCall("DTableService", "getDTable", { code: spreadsheet.code })
      .withSuccessData((data: any) => {
        const spreadsheet = data;
        let observer = new entity.ComplexBeanObserver(spreadsheet);
        let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIDTableEditor appContext={appCtx} pageContext={pageCtx} observer={observer} />
          );
        }
        pageContext.addPageContent('spreadsheet', T(`Spreadsheet ${spreadsheet.label}`), createPageContent);
      })
      .call()
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let spreadsheet = dRecord.record;
    let { appContext, pageContext } = this.props;

    appContext.createHttpBackendCall("DTableService", "getDTable", { code: spreadsheet.code })
      .withSuccessData((data: any) => {
        const spreadsheet = data;
        let plugin = new UIRowListPlugin(this.getVGridContext(), spreadsheet);

        let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (<UIRowList appContext={appCtx} pageContext={pageCtx} type={'page'} plugin={plugin} />);
        }
        pageContext.addPageContent('spreadsheet-data', spreadsheet.label, createPageContent);
      })
      .call()
  }

  onDeleteAction() {
    let callbackConfirm = () => {
      const { appContext, plugin, onModifyBean } = this.props;
      const spreadsheets: Array<any> = plugin.getListModel().getSelectedRecords();
      if (!spreadsheets) return;

      appContext.createHttpBackendCall("DTableService", "rmDTable", { sheets: spreadsheets })
        .withSuccessData((data: any) => {
          appContext.addOSNotification("success", T('Delete Spreadsheet Success'));
          plugin.getListModel().removeSelectedDisplayRecords();
          if (onModifyBean) onModifyBean(plugin.getRecords(), entity.ModifyBeanActions.DELETE);
          else this.reloadData();
        })
        .withFailNotification("danger", T('Delete Spreadsheet Fail!'))
        .call()
    }
    let message = (<div className="text-danger">Do you want to delete these Spreadsheet?</div>);
    bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
  }
}

export class DTablePage extends app.AppComponent {
  render() {
    let { appContext, pageContext } = this.props;
    let ui = (
      <UIDTableList appContext={appContext} pageContext={pageContext} type={'page'} plugin={new UIDTableListPlugin()} />
    );
    return ui;
  }
}