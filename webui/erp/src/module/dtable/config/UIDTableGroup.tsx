import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, input, entity, util } from '@datatp-ui/lib';

import { T } from '../Dependency';
import { UIColumnListEditor } from './UIColumnList';
import { BBRefEmployee } from '../../../module/company/hr';

const { ZERO_AND_GREATER_VALIDATOR } = util.validator;
interface UIColumnGroupProps extends entity.AppDbComplexEntityProps {
  deleteColumnModel?: any;
  onDelete?: (spreadsheet: any) => void;
}

export class UIColumnGroup extends entity.AppDbComplexEntity<UIColumnGroupProps> {
  onModify = (_bean: any, field: string, oldVal: any, newVal: any) => {
    let { onModify, observer } = this.props;
    observer.commitAndGet();
    if (onModify) onModify(observer.getMutableBean(), field, oldVal, newVal);
    else this.forceUpdate();
  }

  onDelete = () => {
    let { appContext, deleteColumnModel, onDelete } = this.props;

    appContext.createHttpBackendCall("DTableService", "deleteDTableColumns", { model: deleteColumnModel })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", T('Delete Group Success'));
        if (onDelete) onDelete(data);
        else this.forceUpdate();
      })
      .call()
  }

  render() {
    let { appContext, pageContext, observer, readOnly, deleteColumnModel, onDelete } = this.props;
    let capability = ['None', 'Read', 'Write', "Moderator", 'Admin'];
    let capabilityLabel = [T('None'), T('Read'), T('Write'), T('Moderator'), T('Admin')];
    let columnGroup = observer.getMutableBean();
    return (
      <div className="flex-vbox">
        <bs.VSplit key={`column-group`}>
          <bs.VSplitPane title='column-info' width={300}>
            <div className="form container-fluid">
              <input.BBNumberField label={T('Index')} bean={columnGroup} field={'idx'} disable={readOnly} onInputChange={this.onModify} validators={[ZERO_AND_GREATER_VALIDATOR]} />
              <input.BBStringField label={T('Label')} bean={columnGroup} field={'label'} disable={readOnly} onInputChange={this.onModify} />
              <input.BBCheckboxField label={T("Visible")} bean={columnGroup} field={'visible'} value={true} disable={readOnly} onInputChange={this.onModify} />
              <input.BBSelectField
                label={T('Min Column Access Capability')} bean={columnGroup} field={"minColumnAccessCapability"}
                options={capability} optionLabels={capabilityLabel} onInputChange={this.onModify} />
            </div>
            <bs.Toolbar hide={readOnly || observer.isNewBean()}>
              <entity.WButtonEntityWrite
                appContext={appContext} pageContext={pageContext} label={T('Delete')} readOnly={readOnly}
                icon={FeatherIcon.Trash} onClick={this.onDelete} />
            </bs.Toolbar>
          </bs.VSplitPane>
          <bs.VSplitPane title='columns'>
            <UIColumnListEditor
              appContext={appContext} pageContext={pageContext} style={{ height: 200 }}
              deleteColumnModel={deleteColumnModel} onDeleteColumn={onDelete}
              plugin={observer.createVGridEntityListEditorPlugin('columns')}
              dialogEditor={true} editorTitle={T("Column")}
              onModifyBean={columns => this.onModify(columnGroup, 'columns', [], columns)} />
          </bs.VSplitPane>
        </bs.VSplit>
      </div>
    );
  }
}

export class UIDTableGroups extends entity.AppDbComplexEntity {
  onDelete = (dtable: any) => {
    let { observer, onModify } = this.props;
    observer.replaceWith(dtable);
    if (onModify) onModify(observer.getMutableBean(), "", null, null);
    else this.forceUpdate();
  }

  render() {
    let { appContext, pageContext, observer, commitTriggers, onModify } = this.props;
    let columnGroups = observer.getComplexArrayProperty('columnGroups', []);
    let groupUIs = [];
    for (let group of columnGroups) {
      let deleteColumnModel = {
        dtable: observer.getMutableBean(),
        columnGroup: group,
      }
      groupUIs.push(
        <bs.Card key={group.name} header={T(`${group.label ? group.label : ""}`)}>
          <UIColumnGroup deleteColumnModel={deleteColumnModel} onDelete={this.onDelete}
            appContext={appContext} pageContext={pageContext}
            observer={new entity.ComplexBeanObserver(group)} commitTriggers={commitTriggers}
            onModify={(group, _field, _oldVal, _newVal) => {
              let index = columnGroups.indexOf(group);
              if (index != -1) columnGroups[index] = group;
              if (onModify) onModify(observer.getMutableBean(), "columnGroups", columnGroups, columnGroups);
              else this.forceUpdate();
            }} />
        </bs.Card>
      )
    }
    return (
      <bs.GreedyScrollable className='flex-vbox'>{groupUIs}</bs.GreedyScrollable>
    );
  }
}