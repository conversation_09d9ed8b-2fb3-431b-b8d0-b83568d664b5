import { app } from "@datatp-ui/lib";
import { DTableColumn, DTableConfig } from "./DTableConfig";

import NONE = app.NONE;
import ADMIN = app.ADMIN;
import MODERATOR = app.MODERATOR;

export class DTablePermissionsHelper {
  accessAccountId: number;
  private spreadsheet: any;
  private permissionMap: Map<number, any>;
  allowedAccessColumnMap: Map<string, DTableColumn>;
  allowedAccessPluginMap: Map<string, any>;

  constructor(accessAccountId: number, spreadsheet: DTableConfig) {
    this.accessAccountId = accessAccountId;
    this.spreadsheet = spreadsheet;

    this.permissionMap = new Map<number, any>();
    for (let sel of spreadsheet.permissions) {
      this.permissionMap.set(sel.userId, sel);
    }
    this.allowedAccessColumnMap = new Map<string, DTableColumn>();
    for (let columnGroup of spreadsheet.columnGroups) {
      for (let column of columnGroup.columns) {
        this.allowedAccessColumnMap.set(column.columnId, column);
      }
    }
    this.allowedAccessPluginMap = new Map<string, any>();
    for (let pluginConfig of spreadsheet.plugins) {
      this.allowedAccessPluginMap.set(pluginConfig.pluginType, pluginConfig);
    }
  }

  hasPermission(requiredCap: app.AppCapability): boolean {
    let permission = this.permissionMap.get(this.accessAccountId);
    if (!permission) return requiredCap == NONE;
    let capability = permission.capability;
    return new app.AppCapability(capability).hasCapability(requiredCap);
  }

  hasAllRowPermissions(): boolean {
    let requiredRowAccessCap = this.spreadsheet.minRowAccessCapability;
    return this.hasPermission(new app.AppCapability(requiredRowAccessCap));
  }

  hasAdminPermission() {
    return this.hasPermission(ADMIN);
  }

  canDeleteRow(row: any) {
    if (this.accessAccountId == row['ownerAccountId']) return true;
    else return this.hasAdminPermission();
  }

  canDeleteAllRow(records: any[]) {
    if (this.hasAdminPermission()) return true;
    for (let row of records) {
      if (this.accessAccountId != row['ownerAccountId']) return false;
    }
    return true;
  }
}
