import React from 'react';
import * as FeatherIcon from 'react-feather';
import { app, bs, input, entity } from '@datatp-ui/lib';

import { T } from '../Dependency';
import { UILoginPermissionListEditor } from './UILoginPermissionList';
import { UIColumnGroup, UIDTableGroups } from './UIDTableGroup';
import { UIDTablePluginConfigListEditor } from './UIDTablePluginConfigListEditor';
import { UIJSMacroListEditor } from './UIJSMacroList';
import { BBRefAccount } from 'module/account';

const SESSION = app.host.DATATP_HOST.session;

class DTableFormSection extends entity.AppDbComplexEntity {
  render() {
    let { appContext, pageContext, observer, readOnly } = this.props;
    let spreadsheet = observer.getMutableBean();
    let capability = ['None', 'Read', 'Write', 'Moderator', 'Admin'];
    let capabilityLabel = [T('None'), T('Read'), T('Write'), T('Moderator'), T('Admin')];
    return (
      <div className="p-1">
        <input.BBStringField
          bean={spreadsheet} field={'label'} label={T('Label')}
          required inputObserver={observer} disable={readOnly} />
        <input.BBStringField
          bean={spreadsheet} field={'code'} label={T('Code')}
          required inputObserver={observer} disable={!observer.isNewBean()} />
        <BBRefAccount
          appContext={appContext} pageContext={pageContext} disable={readOnly}
          label={T('Owner')} placeholder='Enter An Account'
          bean={spreadsheet} accountIdField={'ownerAccountId'} accountLabelField={'ownerLabel'} />
        <input.BBSelectField
          bean={spreadsheet} field={"minRowAccessCapability"} label={T("Min Row Access Capability")}
          options={capability} optionLabels={capabilityLabel} />
        <input.BBCheckboxField
          bean={spreadsheet} field={"summarySupport"}
          label={T("Summary Support")} value={true} />
        <input.BBNumberField
          bean={spreadsheet} field={"summaryCellHeight"} label={T("Summary Cell Height")} />
        <input.BBStringArrayField
          bean={spreadsheet} label={T('Js Plugins')} field={'jsPlugins'} disable={readOnly} />
        <input.BBTextField
          bean={spreadsheet} field={'description'} label={T('Description')}
          style={{ height: '7em' }} disable={readOnly} />
      </div>
    );
  }
}

export class UIDTableEditor extends entity.AppDbComplexEntityEditor {
  commitTriggers = new entity.Triggers();

  onModify = (bean: any, field: string, oldVal: any, newVal: any) => {
    const { observer } = this.props;
    if (field.length > 0) {
      observer.replaceBeanProperty(field, newVal);
    } else {
      observer.setMutableBean(bean);
    }
    this.nextViewId();
    this.forceUpdate();
  }

  onPreCommit = (_observer: entity.IBeanObserver) => {
    let triggers = this.commitTriggers.triggers;
    for (let i = 0; i < triggers.length; i++) {
      let trigger = triggers[i];
      trigger(this);
    }
  }

  onNewColumnGroup = () => {
    const { appContext, pageContext, observer } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let columnGroups: Array<any> = observer.getComplexArrayProperty('columnGroups', []);
    let columnGroup: any = {
      name: `group-${columnGroups.length}`,
      visible: true,
    };
    let columnGroupOb = new entity.ComplexBeanObserver(columnGroup);
    let onAdd = (pageCtx: app.PageContext) => {
      columnGroups.push(columnGroupOb.getMutableBean());
      pageCtx.back({ reload: true });
      this.onModify(observer.getMutableBean(), 'columnGroups', null, columnGroups);
    }

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox'>
          <UIColumnGroup appContext={appCtx} pageContext={pageCtx} observer={columnGroupOb} readOnly={!writeCap} />
          <bs.Toolbar hide={!writeCap}>
            <entity.WButtonEntityWrite
              appContext={appContext} pageContext={pageContext} color='secondary'
              label={T('Add')} icon={FeatherIcon.Plus} onClick={() => onAdd(pageCtx)} />
          </bs.Toolbar>
        </div>
      );
    }
    pageContext.createPopupPage('add-column-group', T('Add Column Group'), createAppPage, { size: 'lg', backdrop: 'static' });
  }

  render() {
    let { observer, readOnly, appContext, pageContext } = this.props;
    let spreadsheet = observer.getMutableBean();
    this.commitTriggers.clear(this.viewId);
    return (
      <div key={this.viewId} className='flex-vbox'>
        <bs.VSplit updateOnResize>
          <bs.VSplitPane title='Spreadsheet' width={550}>
            <bs.Card header={T("Info")}>
              <bs.GreedyScrollable className='flex-vbox' style={{ minHeight: 300 }}>
                <DTableFormSection
                  appContext={appContext} pageContext={pageContext} observer={observer} />
              </bs.GreedyScrollable>
            </bs.Card>
            <bs.TabPane key={`spreadsheet-tabs`} >
              <bs.Tab name='permissions-tab' label='Permissions' active>
                <UILoginPermissionListEditor
                  appContext={appContext} pageContext={pageContext}
                  plugin={observer.createVGridEntityListEditorPlugin('permissions')}
                  editorTitle={T("Permission")} dialogEditor={true} />
              </bs.Tab>
              <bs.Tab name='plugin-configs-tab' label='Plugins'>
                <UIDTablePluginConfigListEditor
                  appContext={appContext} pageContext={pageContext}
                  plugin={observer.createVGridEntityListEditorPlugin('plugins', [])}
                  editorTitle={T("Plugin")} dialogEditor={true} />
              </bs.Tab>
              <bs.Tab name='macros-tab' label='JS Macros'>
                <UIJSMacroListEditor
                  appContext={appContext} pageContext={pageContext}
                  plugin={observer.createVGridEntityListEditorPlugin('jsMacros')}
                  editorTitle={T("JS Macro")} dialogEditor={true} />
              </bs.Tab>
            </bs.TabPane>
          </bs.VSplitPane>
          <bs.VSplitPane>
            <div className='flex-vbox'>
              <UIDTableGroups appContext={appContext} pageContext={pageContext}
                observer={observer} commitTriggers={this.commitTriggers} onModify={this.onModify} />
            </div>
          </bs.VSplitPane>
        </bs.VSplit>

        <bs.Toolbar hide={readOnly}>
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext} readOnly={readOnly} color='secondary'
            icon={FeatherIcon.Plus} label={T('Add Group')} onClick={this.onNewColumnGroup} />
          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext} observer={observer}
            commit={{
              entityLabel: T(`Spreadsheet {{label}}`, { label: spreadsheet.label }),
              context: 'company', service: 'DTableService', commitMethod: 'saveDTable'
            }}
            onPreCommit={this.onPreCommit}
            onPostCommit={this.onPostCommit} />
          <entity.WButtonEntityReset
            appContext={appContext} pageContext={pageContext} readOnly={readOnly} observer={observer}
            onPostRollback={(entity) => this.onPostRollback(entity)} />
        </bs.Toolbar>
      </div>
    );
  }
}
