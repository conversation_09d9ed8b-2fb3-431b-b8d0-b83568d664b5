import React, { ReactElement } from "react";
import { app, bs, chart } from "@datatp-ui/lib";

import { T } from "../../Dependency";
import { IST_REPORT_COLOR, ISTPieChartTemplate } from "./DTableBDISTDailyReportPlugin";

export class ISTPieChartTemplateModel extends chart.PieChartTemplateModel<any> {
  label: string = 'VOLUME';

  constructor(config: chart.PieChartTemplateConfig, records: Array<any>) {
    super(config, records);
  }

  override renderLegendContent = (pieProps: any): ReactElement => {
    const { payload } = pieProps;
    let total: number = 0;
    let html = (
      <div style={{ fontSize: 10, overflow: "scroll", maxHeight: 350, width: 300 }}>
        <table className='table table-sm table-hover'>
          <thead>
            <tr>
              <th scope="col">SALEMAN</th>
              <th scope="col">{this.label}</th>
              <th scope="col">PERCENT</th>
            </tr>
          </thead>
          <tbody>
            {payload.map((entry: any, index: any) => {
              if (entry.payload.isEmpty) {
                return (
                  <tr key={index} className="fw-bold" style={{ color: '#666' }}>
                    <td scope="row">NO DATA</td>
                    <td scope="row" className='text-right'>0</td>
                    <td scope="row" className='text-right'>-</td>
                  </tr>)
              }
              total += entry.payload.value
              return (
                <tr key={index}>
                  <td scope="row" className="fw-bold" style={{ color: IST_REPORT_COLOR[index % IST_REPORT_COLOR.length] }}> {entry.payload.name} </td>
                  <td scope="row" className='text-right' style={{ color: IST_REPORT_COLOR[index % IST_REPORT_COLOR.length] }}> {entry.payload.value} </td>
                  <td scope="row" style={{ color: IST_REPORT_COLOR[index % IST_REPORT_COLOR.length] }}> {`${entry.payload.percent ? (entry.payload.percent * 100).toFixed(0) : 0}%`} </td>
                </tr>)
            })}
            <tr className='fw-bold'>
              <td scope="row text-primary">TOTAL</td>
              <td scope="row text-primary" className='text-right'>{total}</td>
              <td scope="row"></td>
            </tr>
          </tbody>
        </table>
      </div>
    )
    return html;
  }

  override computeData() {
    let salemanMap: Map<number, string> = new Map<number, string>();
    for (let record of this.records) {
      salemanMap.set(record.ownerAccountId, record.ownerLabel);
    }
    let recordMap: Map<number, number> = new Map<number, number>();
    for (let record of this.records) {
      if (!recordMap.has(record['ownerAccountId'])) {
        recordMap.set(record['ownerAccountId'], this.calculateVolume(record));
      } else {
        let total = recordMap.get(record['ownerAccountId'])!;
        let volume = this.calculateVolume(record);
        total += volume
        recordMap.set(record['ownerAccountId'], total);
      }
    }
    let records = Array.from(recordMap, ([key, value]) => {
      return { 'name': salemanMap.get(key), 'value': value };
    });

    // Check if all values are 0
    const totalValue = records.reduce((sum, record) => sum + record.value, 0);
    if (totalValue === 0) {
      // Return single entry for empty state
      return [{ 'name': 'NO DATA', 'value': 1, 'isEmpty': true }];
    }

    return records;
  }

  calculateVolume = (rec: any) => {
    return 1;
  }

  override formatTooltip(value: any) {
    return value;
  }

  override renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, name }: any): any => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);
    return (
      <text
        x={x} y={y} textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central"
        className='fw-bold' fill='white' style={{ fontSize: 10 }}>
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };
}

class TEUPieChart extends ISTPieChartTemplateModel {
  label = 'TEU';

  calculateVolume = (rec: any) => {
    let volume = 0;
    if (rec['status'] !== 'Won') return volume;
    if (rec['exp-20'] && rec['exp-20'] !== 0) volume += rec['exp-20'];
    if (rec['exp-40'] && rec['exp-40'] !== 0) volume += rec['exp-40'] * 2;
    if (rec['exp-45'] && rec['exp-45'] !== 0) volume += rec['exp-45'] * 2;
    if (rec['imp-20'] && rec['imp-20'] !== 0) volume += rec['imp-20'];
    if (rec['imp-40'] && rec['imp-40'] !== 0) volume += rec['imp-40'] * 2;
    if (rec['imp-45'] && rec['imp-45'] !== 0) volume += rec['imp-45'] * 2;
    return volume;
  }
}

class CBMPieChart extends ISTPieChartTemplateModel {
  label = 'CBM';

  calculateVolume = (rec: any) => {
    let volume = 0;
    if (rec['status'] !== 'Won') return volume;
    if (rec['exp-lcl-cbm'] && rec['exp-lcl-cbm'] !== 0) volume += rec['exp-lcl-cbm'];
    if (rec['imp-lcl-cbm'] && rec['imp-lcl-cbm'] !== 0) volume += rec['imp-lcl-cbm'];
    return volume;
  }
}

class KGPieChart extends ISTPieChartTemplateModel {
  label = 'KG';

  calculateVolume = (rec: any) => {
    let volume = 0;
    if (rec['status'] !== 'Won') return volume;
    if (rec['exp-air-kgs'] && rec['exp-air-kgs'] !== 0) volume += rec['exp-air-kgs'];
    if (rec['imp-air-kgs'] && rec['imp-air-kgs'] !== 0) volume += rec['imp-air-kgs'];
    return volume;
  }
}

class ShipmentPieChart extends ISTPieChartTemplateModel {
  label = 'SHIPMENT';

  calculateVolume = (rec: any) => {
    let volume = 0;
    if (rec['status'] !== 'Won') return volume;
    if (rec['exp-breakbulk-shmt'] && rec['exp-breakbulk-shmt'] !== 0) volume += rec['exp-breakbulk-shmt'];
    if (rec['breakbulk-shpt-i'] && rec['breakbulk-shpt-i'] !== 0) volume += rec['breakbulk-shpt-i'];
    return volume;
  }
}

interface VolumeRatioProps extends app.AppComponentProps {
  records: Array<any>;
  model: chart.PieChartTemplateModel<any>;
}
export class VolumeRatio extends app.AppComponent<VolumeRatioProps> {
  render(): React.ReactNode {
    let { appContext, records, model } = this.props;
    if (!records) records = [];
    if (records.length == 0 || !records) {
      appContext.addOSNotification("danger", T("No Records were found"));
      return;
    } else {
      return (
        <ISTPieChartTemplate model={model} />
      )
    }
  }
}
interface UIReportIndividualVolumeProps extends app.AppComponentProps {
  records: Array<any>;
}
export class UIReportIndividualVolume extends app.AppComponent<UIReportIndividualVolumeProps> {
  render() {
    let { appContext, pageContext, records } = this.props;
    const CONFIG: chart.PieChartTemplateConfig = {
      radius: 100,
      labelLine: false,
      customLabel: true,
      customLegend: true,
      height: 350,
    }
    return (
      <div className="justify-content-center" style={{ display: 'flex', flexWrap: 'wrap', gap: '16px', justifyContent: 'flex-start' }}>
        <div className="flex-hbox" style={{ flex: '0 0 auto' }}>
          <div style={{ width: 350, marginLeft: 16, marginRight: 16 }}>
            <bs.Card header={T("TEU")} key={'teu-ratio'}>
              <VolumeRatio appContext={appContext} pageContext={pageContext} records={records} model={new TEUPieChart(CONFIG, records)} />
            </bs.Card>
          </div>
          <div style={{ width: 350 }}>
            <bs.Card header={T("CBM")} key={'cbm-ratio'}>
              <VolumeRatio appContext={appContext} pageContext={pageContext} records={records} model={new CBMPieChart(CONFIG, records)} />
            </bs.Card>
          </div>
        </div>
        <div className="flex-hbox" style={{ flex: '0 0 auto' }}>
          <div style={{ width: 350, marginRight: 16 }}>
            <bs.Card header={T("KG")} key={'kg-ratio'}>
              <VolumeRatio appContext={appContext} pageContext={pageContext} records={records} model={new KGPieChart(CONFIG, records)} />
            </bs.Card>
          </div>
          <div style={{ width: 350, marginRight: 16 }}>
            <bs.Card header={T("SHIPMENT (BREAKBULK)")} key={'shipment-ratio'}>
              <VolumeRatio appContext={appContext} pageContext={pageContext} records={records} model={new ShipmentPieChart(CONFIG, records)} />
            </bs.Card>
          </div>
        </div>
      </div >
    );
  }
}

class InquiryPieChart extends ISTPieChartTemplateModel {
  label = 'INQUIRIES';

  override computeData() {
    let salemanMap: Map<number, string> = new Map<number, string>();
    for (let record of this.records) {
      salemanMap.set(record.ownerAccountId, record.ownerLabel);
    }
    let recordMap: Map<number, number> = new Map<number, number>();
    for (let record of this.records) {
      if (!recordMap.has(record['ownerAccountId'])) {
        recordMap.set(record['ownerAccountId'], 1);
      } else {
        let total = recordMap.get(record['ownerAccountId'])!;
        total++;
        recordMap.set(record['ownerAccountId'], total);
      }
    }
    let records = Array.from(recordMap, ([key, value]) => {
      return { 'name': salemanMap.get(key), 'value': value };
    });

    // Check if all values are 0
    const totalValue = records.reduce((sum, record) => sum + record.value, 0);
    if (totalValue === 0) {
      // Return single entry for empty state
      return [{ 'name': 'NO DATA', 'value': 1, 'isEmpty': true }];
    }

    return records;
  }
}

interface ReportIndividualInquiryProps extends app.AppComponentProps {
  records?: Array<any>;
}

export class ReportIndividualInquiry extends app.AppComponent<ReportIndividualInquiryProps> {
  render(): React.ReactNode {
    let { appContext, records } = this.props;
    if (!records) records = [];
    if (records.length == 0 || !records) {
      appContext.addOSNotification("danger", T("No Records were found"));
      return;
    } else {
      const CONFIG: chart.PieChartTemplateConfig = {
        radius: 150,
        labelLine: false,
        customLabel: true,
        customLegend: true,
        height: 500,
      }
      return (
        <ISTPieChartTemplate model={new InquiryPieChart(CONFIG, records)} />
      )
    }
  }
}

class ShipmentWonPieChart extends ISTPieChartTemplateModel {
  label = 'SHIPMENT WON';

  calculateVolume = (rec: any) => {
    return rec['booking-qty'] ? rec['booking-qty'] : 0;
  }
}

export class ReportIndividualShipmentWon extends app.AppComponent<ReportIndividualInquiryProps> {
  render(): React.ReactNode {
    let { appContext, records } = this.props;
    if (!records) records = [];
    if (records.length == 0 || !records) {
      appContext.addOSNotification("danger", T("No Records were found"));
      return;
    } else {
      const CONFIG: chart.PieChartTemplateConfig = {
        radius: 150,
        labelLine: false,
        customLabel: true,
        customLegend: true,
        height: 500,
      }
      return (
        <ISTPieChartTemplate model={new ShipmentWonPieChart(CONFIG, records)} />
      )
    }
  }
}