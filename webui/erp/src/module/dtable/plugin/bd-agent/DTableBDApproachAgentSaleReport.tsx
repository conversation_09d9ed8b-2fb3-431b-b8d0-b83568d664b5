import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, grid, entity } from "@datatp-ui/lib";

import { T } from "../../Dependency";
import { UIRowList } from "../../data/UIRowList";
import { UIRowListPlugin } from "../../data/UIRowListPlugin";

interface XlsxExportButtonProps extends entity.XlsxExportButtonProps {
  periodNote?: string;
}
export class XlsxExportButton extends entity.XlsxExportButton<XlsxExportButtonProps> {
  override customDataListExportModel = (model: entity.DataListExportModel) => {
    let { periodNote } = this.props;
    model["fileName"] = "MONTHLY REPORT: RESULT OF AGENCY SALES.xlsx"
    if (!model['xlsxTemplate']) {
      model["xlsxTemplate"] = {
        banner: {
          rows: [
            {
              dataType: 'primitive',
              cells: [
                {
                  rowHeight: 1,
                  colStart: 0,
                  colEnd: 7,
                  dataType: 'string',
                  value: "MONTHLY REPORT - SALES RESULT OF AGENCY SALES",
                  style: {
                    align: "CENTER",
                    valign: "CENTER",
                    bold: true,
                    fontSize: 16,
                    textWrap: true
                  }
                }
              ]
            },
            {
              dataType: 'blank',
              cells: []
            },
            {
              dataType: "primitive",
              rowspan: 1,
              cells: [
                {
                  colStart: 5,
                  colEnd: 5,
                  dataType: 'string',
                  value: periodNote,
                  style: {
                    align: "LEFT",
                    valign: "CENTER",
                    bold: true,
                    fontSize: 12,
                    textWrap: true
                  }
                }
              ]
            }
          ]
        }
      }
    };
    return model;
  }

  override render() {
    return (
      <bs.Button laf="primary" className="p-1" onClick={this.onExportCustomization}>
        <FeatherIcon.Download size={12} /> {T('XLSX Export')}
      </bs.Button>
    )
  }
}

interface UIReportProps extends entity.DbEntityListProps {
  periodNote?: string;
}
class UIReport extends entity.DbEntityList<UIReportProps> {
  createVGridConfig() {
    let { periodNote } = this.props;
    let config: grid.VGridConfig = {
      record: {
        computeDataRowHeight(ctx, record) {
          return 45;
        },
        fields: [
          { name: "saleman", label: T("Saleman"), width: 300, state: { visible: false } },
          { name: 'source', label: T('Source'), width: 136 },
          { name: 'inquiry', label: T('Inquiry'), width: 100 },
          { name: 'booking', label: T('Booking'), width: 100 },
          { name: 'salePercent', label: T("% Chốt Sale"), width: 100 },
          { name: 'comment', label: T("Reason/Comment"), width: 400, editor: { enable: true, type: 'string' } }
        ],
      },
      toolbar: {
        actions: [
          {
            name: "export-xlsx", label: 'Export Xlsx',
            createComponent: function (ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as any;
              const { appContext, pageContext } = uiRoot.props;
              return (<XlsxExportButton appContext={appContext} pageContext={pageContext} context={ctx} periodNote={periodNote} />)
            }
          },
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(false)
      },
      view: {
        currentViewName: 'aggregation',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 450,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel("All", false);
              model.addAggregation(new grid.ValueAggregation('Saleman', 'saleman', true));
              model.addAggregation(new grid.ValueAggregation('Source', 'source', false));
              return model;
            }
          }
        }
      }
    }
    return config;
  }
}

const report = (context: grid.VGridContext) => {
  let uiRoot = context.uiRoot as UIRowList;
  let { appContext, pageContext, plugin } = uiRoot.props;
  let pluginImpl = plugin as UIRowListPlugin;
  let permissions = pluginImpl.spreadsheet._spreadsheet.permissions;
  let salemanMap: Map<number, string> = new Map<number, string>();
  for (let permission of permissions) {
    salemanMap.set(permission.userId, permission.label);
  }

  let records = plugin.getListModel().getFilterRecords() ?? [];
  let holder: any[] = [];
  let mapBySaleman: Map<number, Array<any>> = new Map();

  for (let record of records) {
    let recs = mapBySaleman.get(record['ownerAccountId']);
    if (recs && recs.length > 0) {
      mapBySaleman.set(record['ownerAccountId'], [...recs, record]);
    } else {
      mapBySaleman.set(record['ownerAccountId'], [record]);
    }
  }

  for (let accountId of mapBySaleman.keys()) {
    let mapBySource: Map<string, Array<any>> = new Map([
      ['WCA', []],
      ['GLA', []],
      ['JC TRANS', []],
      ['FM', []],
      ['PCN', []],
      ['GAA', []],
      ['OLO', []],
      ['DIRECT CUSTOMER', []],
    ]);
    let recs = mapBySaleman.get(accountId);
    if (recs && recs.length > 0) {
      for (let rec of recs) {
        if (rec['source'] === 'WCA') mapBySource.get('WCA')?.push(rec);
        else if (rec['source'] === 'GLA') mapBySource.get('GLA')?.push(rec);
        else if (rec['source'] === 'JC TRANS') mapBySource.get('JC TRANS')?.push(rec);
        else if (rec['source'] === 'FM') mapBySource.get('FM')?.push(rec);
        else if (rec['source'] === 'PCN') mapBySource.get('PCN')?.push(rec);
        else if (rec['source'] === 'GAA') mapBySource.get('GAA')?.push(rec);
        else if (rec['source'] === 'OLO') mapBySource.get('OLO')?.push(rec);
        else if (rec['source'] === 'DIRECT CUSTOMER') mapBySource.get('DIRECT CUSTOMER')?.push(rec);
      }
    }

    for (let source of mapBySource.keys()) {
      let recs = mapBySource.get(source);
      let inquiryCount = 0;
      let bookingCount = 0;
      if (recs && recs.length > 0) {
        for (let rec of recs) {
          if (rec['inquiry']) inquiryCount += parseInt(rec['inquiry']);
          if (rec['booking']) bookingCount += parseInt(rec['booking']);
        }
      }
      holder.push(
        {
          saleman: (salemanMap.get(accountId) + ' - ' + accountId).toUpperCase(),
          source: source,
          inquiry: inquiryCount,
          booking: bookingCount,
          salePercent: inquiryCount > 0 ? (bookingCount * 100 / inquiryCount).toFixed(2) : 0,
          comment: '',
        }
      )
    }
  }
  let periodNote = "";
  for (let fieldConfig of context.config.record.fields) {
    if (fieldConfig.name === 'approach-date') {
      let filterValue = fieldConfig.state?.filterValue
      if (filterValue) {
        if (!filterValue.fromValue && !filterValue.toValue) break;
        let fromValue: string = filterValue.fromValue;
        if (fromValue && fromValue.length > 10) periodNote += fromValue.substring(0, 10) + ' - ';
        else periodNote += 'N/A - ';
        let toValue = filterValue.toValue;
        if (toValue && toValue.length > 10) periodNote += toValue.substring(0, 10);
        else periodNote += 'N/A';
      }
      break;
    }
  }
  let ui = () => <UIReport appContext={appContext} pageContext={pageContext} plugin={new entity.DbEntityListPlugin(holder)} periodNote={periodNote} />
  context.getVGrid().addTab("Agency Sales - Month Report", ui);
  context.getVGrid().forceUpdateView();
}

export const ReportPlugin: grid.VGridActionConfig = {
  name: 'bang-theo-doi-approach-agent-customer-bd',
  label: 'Agency Sales - Month Report',
  icon: FeatherIcon.FileText,
  onClick: report
}