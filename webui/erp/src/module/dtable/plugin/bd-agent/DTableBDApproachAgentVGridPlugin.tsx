import { grid } from '@datatp-ui/lib';

import { DTableVGridPlugin } from "../DTableVGridPlugin";
import { ReportPlugin } from './DTableBDApproachAgentSaleReport';
import React from 'react';

export const DTableBDApproachAgentVGridPlugin: DTableVGridPlugin = {
  name: 'bang-theo-doi-approach-agent-customer-bd-vgrid-plugin', label: 'Actions',
  isApplyFor(ctx: grid.VGridContext) {
    return true;
  },
  toolbar: {
    actions: [
      ReportPlugin
    ],
    dropdownActions: [
    ]
  },
  onInitVGrid(context: grid.VGridContext) {
    context.config.footer = {
      default: {
        render: (ctx: grid.VGridContext) => {
          let records = ctx.model.getFilterRecords();
          return (
            <div className='flex-hbox align-items-center justify-content-end flex-wrap flex-grow-0'>
              <p className="text-800 fs--1 mb-0 fw-bold px-1">Total Inquiry: {records.length}</p>
            </div>);
        }
      },
      ...context.config.footer
    }
  },
}