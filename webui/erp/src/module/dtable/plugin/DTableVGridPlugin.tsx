import { grid } from '@datatp-ui/lib';

import { T } from "../Dependency";
import { DTableBDApproachAgentVGridPlugin } from "./bd-agent/DTableBDApproachAgentVGridPlugin";
import { DTableBDQuotationDailyVGridPlugin } from './bd-quotation/DTableBDQuotationDailyVGridPlugin';
import { DTableBDISTDailyVGridPlugin } from './bd-ist/DTableBDISTDailyVGridPlugin';

export type DTableVGridPlugin = {
  name: string;
  label: string;

  toolbar?: {
    actions?: Array<grid.VGridActionConfig>;
    dropdownActions?: Array<grid.VGridDropdownActionConfig>;
  }

  isApplyFor: (ctx: grid.VGridContext) => boolean;
  onInitVGrid?: (ctx: grid.VGridContext) => void;
}

class VGridPluginManager {
  pluginMap: Record<string, DTableVGridPlugin> = {};
  getPlugin(pluginName: string) {
    if (!this.pluginMap[pluginName]) throw new Error(T("No plugin registered!"));
    return this.pluginMap[pluginName];
  }

  register(pluginName: string, plugin: DTableVGridPlugin) {
    if (this.pluginMap[pluginName]) throw new Error(T("Plugin already registered!"));
    this.pluginMap[pluginName] = plugin;
  }
}

const DTableVGridPluginManager = new VGridPluginManager();

DTableVGridPluginManager.register(DTableBDApproachAgentVGridPlugin.name, DTableBDApproachAgentVGridPlugin)
DTableVGridPluginManager.register(DTableBDQuotationDailyVGridPlugin.name, DTableBDQuotationDailyVGridPlugin)
DTableVGridPluginManager.register(DTableBDISTDailyVGridPlugin.name, DTableBDISTDailyVGridPlugin)
export { DTableVGridPluginManager }