import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather';
import { server, bs, grid, entity, app, component } from '@datatp-ui/lib';


import { T } from '../Dependency';
import * as common from '../../common';
import { DTableRestURL } from '../RestURL';
import { UIRowList } from './UIRowList';
import { UIRowListPlugin } from './UIRowListPlugin';
import { DTable, DTableConfig } from '../config/DTableConfig';
import { DTablePermissionsHelper } from '../config/DTablePermissionHelper';
import { UIDTableEditor } from '../config/UIDTable';
const SESSION = app.host.DATATP_HOST.session;

class XLSXButton extends entity.XlsxExportButton {
  override customDataListExportModel = (model: entity.DataListExportModel) => {
    let { context } = this.props;
    model["fileName"] = (context.config.id ? context.config.id : 'SpreadSheet') + ".xlsx"
    for (let fieldGroup of model.fieldGroups) {
      if (!fieldGroup.label) fieldGroup.label = '_blank_'
      for (let field of fieldGroup.fields) {
        field.dataType = field.dataType?.toLowerCase();
      }
    }

    for (let field of model.fields) {
      field.dataType = field.dataType?.toLowerCase();
    }
    return model;
  }
}
export class UIRowListControl extends Component<grid.VGridContextProps> {
  onSaveRows = (successCb: (data: any) => void) => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIRowList;
    let { appContext, plugin } = uiRoot.props;
    let pluginImpl = plugin as UIRowListPlugin;
    let recordConfig = context.config.record;

    let holder: any[] = [];
    for (let row = 0; row < context.model.getRecords().length; row++) {
      let record = context.model.getRecords()[row];
      let state = grid.getRecordState(record);
      if (state.isMarkDeleted() && !record['id']) {
        pluginImpl.getListModel().removeRecord(record);
        continue;
      }
      if (state.isMarkModified() || state.isMarkDeleted() || state.isMarkNew()) {
        holder.push(record);
      }
    }

    let failCB = (response: server.BackendResponse) => {
      let title = T('Save DTable Rows Fail!');
      appContext.addOSNotification('danger', title, response.error, response);
      let errorType = response.error.errorType;
      let message;
      if (errorType === 'EntityModified') {
        message = "Your data has been modified by another. Reload the data and update your change!"
      } else {
        message = response.error.message;
      }
      let messageContent = message.split('\n').map((sel: string) => <div>{sel}</div>);
      bs.notificationShow("danger", title, <div className='alert alert-danger'>{messageContent}</div>);
      uiRoot.sort(plugin.getListModel().getRecords());
      plugin.update(plugin.getListModel().getRecords());
      context.getVGrid().forceUpdateView();
    }

    let dEntities = entity.EntityListUtil.toDynamicEntities(recordConfig, holder, (entity, dynamicEntity) => {
      dynamicEntity['id'] = entity['id'];
      dynamicEntity['dtableId'] = entity['dtableId'];
      dynamicEntity['ownerAccountId'] = entity['ownerAccountId'];
      dynamicEntity['ownerLabel'] = entity['ownerLabel'];
      dynamicEntity['_rowHeight_'] = entity['_rowHeight_'];
    });

    appContext.createHttpBackendCall("DTableService", "saveDTableRows", { spreadsheetCode: pluginImpl.spreadsheet.getConfig().code, dRows: dEntities })
      .withSuccessData(successCb)
      .withFail(failCB)
      .call()
  }

  onSaveAndRefresh = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIRowList;
    let { appContext, pageContext, plugin } = uiRoot.props;
    let pluginImpl = plugin as UIRowListPlugin;
    let spreadsheet = pluginImpl.spreadsheet._spreadsheet;
    let successCb = (data: any) => {
      pageContext.back();
      appContext.createHttpBackendCall("DTableService", "getDTable", { code: spreadsheet.code })
        .withSuccessData((data: any) => {
          const spreadsheet = data;
          let plugin = new UIRowListPlugin(uiRoot.getVGridContext(), spreadsheet);
          let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (<UIRowList appContext={appContext} pageContext={pageContext} type={'page'} plugin={plugin} />);
          }
          pageContext.addPageContent('spreadsheet-data', spreadsheet.label, createPageContent);
        }).call()
    }

    this.onSaveRows(successCb);
  }

  onSave = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIRowList;
    let { appContext, plugin } = uiRoot.props;
    let pluginImpl = plugin as UIRowListPlugin;

    let holder: any[] = [];
    for (let row = 0; row < context.model.getRecords().length; row++) {
      let record = context.model.getRecords()[row];
      let state = grid.getRecordState(record);
      if (state.isMarkDeleted() && !record['id']) {
        pluginImpl.getListModel().removeRecord(record);
        continue;
      }
      if (state.isMarkModified() || state.isMarkDeleted() || state.isMarkNew()) {
        holder.push(record);
      }
    }

    let successCb = (dRows: any) => {
      for (let i = 0; i < holder.length; i++) {
        let modRecord = holder[i];
        if (modRecord['_state'].deleted) {
          pluginImpl.getListModel().removeRecord(modRecord);
        } else {
          let dRow = dRows[i];
          modRecord['id'] = dRow['id'];
          modRecord['dtableId'] = dRow['dtableId'];
          modRecord['ownerAccountId'] = dRow['ownerAccountId'];
          modRecord['ownerLabel'] = dRow['ownerLabel'];
        }
      }
      let records = pluginImpl.getListModel().getRecords();
      for (let record of records) {
        grid.initRecordState(record, record['row'])
      }
      appContext.addOSNotification("success", `Edit Rows Success`);
      uiRoot.onPostLoadData(plugin.getListModel().getRecords());
      plugin.update(plugin.getListModel().getRecords());
      context.getVGrid().forceUpdateView();
    }
    this.onSaveRows(successCb);
  }

  exportTemplate = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIRowList;
    let { appContext, plugin } = uiRoot.props;
    let pluginImpl = plugin as UIRowListPlugin;
    let dtable: DTable = pluginImpl.spreadsheet;
    const callback = (response: server.BackendResponse) => {
      let storeInfo = response.data;
      common.StoreInfo.privateDownload(storeInfo);
    }
    appContext.DO_NOT_USE_serverPOST(DTableRestURL.exportTemplate, dtable.getConfig(), callback);
  }

  onEditSpreadsheetConfig = (dtable: DTableConfig) => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIRowList;
    let { appContext, pageContext } = uiRoot.props;
    if (!pageContext.hasUserAdminCapability() && dtable.ownerAccountId !== SESSION.getAccountId()) return;

    appContext.createHttpBackendCall("DTableService", "getDTable", { code: dtable.code })
      .withSuccessData((data: any) => {
        const spreadsheet = data;
        let observer = new entity.ComplexBeanObserver(spreadsheet);
        let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (<UIDTableEditor appContext={appCtx} pageContext={pageCtx} observer={observer} />);
        }
        pageContext.addPageContent('spreadsheet', T(`Spreadsheet ${spreadsheet.label}`), createPageContent);
      })
      .call()
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIRowList;
    let { appContext, pageContext, plugin, readOnly } = uiRoot.props;
    let pluginImpl = plugin as UIRowListPlugin;
    let dtable = pluginImpl.spreadsheet._spreadsheet;
    let permissionHelper: DTablePermissionsHelper = pluginImpl.spreadsheet.getPermissionHelper();
    let smallScreen = bs.ScreenUtil.isSmallScreen();

    return (
      <bs.Toolbar hide={readOnly || !permissionHelper.hasPermission(app.WRITE)}>
        <entity.WButtonEntityWrite icon={FeatherIcon.Edit} color='secondary'
          appContext={appContext} pageContext={pageContext}
          label={T('Config')} disable={!pageContext.hasUserAdminCapability() && dtable.ownerAccountId !== SESSION.getAccountId()}
          onClick={() => { this.onEditSpreadsheetConfig(dtable) }} />
        <XLSXButton appContext={appContext} pageContext={pageContext} context={context} />
        <entity.WButtonEntityWrite icon={FeatherIcon.Download} color='secondary'
          appContext={appContext} pageContext={pageContext}
          label={T('Download Template')} onClick={this.exportTemplate} />
        {
          permissionHelper.hasPermission(app.WRITE) ?
            <component.WUploadResource readOnly={!smallScreen} laf='secondary'
              appContext={appContext} pageContext={pageContext}
              label={T('Upload')} multiple={false} maxSize={1024 * 1024 * 32}
              onUploadSuccess={(uploadResources: any) => uiRoot.onUploadSuccess(uploadResources[0], false)}
              onRemoveSuccess={(resource: any) => uiRoot.onDeleteUploadResourceSuccess(resource)} />
            : null
        }
        <entity.WButtonEntityWrite icon={FeatherIcon.Edit}
          appContext={appContext} pageContext={pageContext}
          label={T('Save & Refresh')} onClick={this.onSaveAndRefresh} />
        <entity.WButtonEntityWrite icon={FeatherIcon.Edit}
          appContext={appContext} pageContext={pageContext}
          label={T('Save Changes')} onClick={this.onSave} />
      </bs.Toolbar>
    )
  }
}
