import React from 'react';
import * as FeatherIcon from 'react-feather';
import { server, app, util, grid, bs, sql, input, entity, component } from '@datatp-ui/lib';

import { T } from '../Dependency';
import { DTablePermissionsHelper } from '../config/DTablePermissionHelper';
import { UIRowListControl } from './UIRowListControl';
import {
  AutocompleteType, DTable, DTableConfig
} from '../config/DTableConfig';
import { UIRowListPlugin } from './UIRowListPlugin';
import { DTableVGridPlugin, DTableVGridPluginManager } from '../plugin/DTableVGridPlugin';

import { DTableMacroContext } from './DTableMacro';
import { createVGridFavButton } from 'module/settings/ui/favorite';

const SESSION = app.host.DATATP_HOST.session;

export class UIRowList extends entity.DbEntityList {
  protected createVGridContext() {
    let { plugin } = this.props;
    let pluginImpl = plugin as UIRowListPlugin;
    let spreadsheet = pluginImpl.spreadsheet;
    let config = this.createVGridConfig();
    let context = new grid.VGridContext(this);
    let jsMacros: Array<any> = spreadsheet.getConfig().jsMacros;
    let evalmacroContext = new DTableMacroContext(this, context);
    for (let jsMacro of jsMacros) {
      evalmacroContext.run(jsMacro);
    }

    context.init(config, pluginImpl.getListModel());

    let pluginNames: string[] = spreadsheet._spreadsheet.jsPlugins ?? [];
    for (let pluginName of pluginNames) {
      if (!pluginName) continue;
      let plugin: DTableVGridPlugin = DTableVGridPluginManager.getPlugin(pluginName);
      let support = plugin.isApplyFor(context);
      if (!support) continue;

      if (plugin.toolbar) {
        if (plugin.toolbar.actions) plugin.toolbar.actions.map((act) => config.toolbar.actions?.unshift(act));
        if (plugin.toolbar.dropdownActions) config.toolbar.dropdownActions = plugin.toolbar.dropdownActions;
      }
      if (plugin.onInitVGrid) plugin.onInitVGrid(context);
    }

    return context;
  }

  getTime = (bean: any, fieldName: string): number | null => {
    let val = bean[fieldName];
    if (!val) return null;
    if (typeof val === "string") {
      const newVal = val.substring(0, 10);
      const pattern = /(\d{2})\/(\d{2})\/(\d{4})/;
      val = new Date(newVal.replace(pattern, "$3-$2-$1"));
    }
    return val.getTime();
  }

  sort(records: Array<any>) {
    //sort algorithms
    let { plugin } = this.props;
    let pluginImpl = plugin as UIRowListPlugin;
    let spreadsheet = pluginImpl.spreadsheet;
    let sortableColumns = spreadsheet.getSortableColumns();
    return records.sort((a, b) => {
      for (let i = 0; i < sortableColumns.length; i++) {
        const column = sortableColumns[i];
        const { type, sortDirection, columnId } = column;
        let valueA = a[columnId];
        let valueB = b[columnId];
        if (type === 'Date') {
          valueA = this.getTime(a, columnId);
          valueB = this.getTime(b, columnId);
        } else if (type === 'String') {
          valueA = valueA && valueA.toUpperCase();
          valueB = valueB && valueB.toUpperCase();
        }

        if (!valueA && !valueB) {
          continue;
        } else if (!valueA) {
          return sortDirection === 'ASC' ? 1 : -1; // Place at the bottom of the list
        } else if (!valueB) {
          return sortDirection === 'ASC' ? -1 : 1; // Place at the bottom of the list
        }
        if (valueA < valueB) return sortDirection === 'ASC' ? -1 : 1;
        if (valueA > valueB) return sortDirection === 'ASC' ? 1 : -1;
      }
      return 0;
    });
  }

  override onPostLoadData(records: any[]): void {
    this.getVGridContext().broadcastDataLoad(records);
    if (!this.getVGridContext().listeners['sort']) this.sort(records);
  }

  createVGridConfig() {
    let { plugin, type, appContext, pageContext } = this.props;
    let enableEditor: boolean = type === 'selector' ? false : true;
    let smallScreen = bs.ScreenUtil.isSmallScreen();
    const writeCap = pageContext.hasUserWriteCapability();
    let thisUI = this;
    let hideWriteAction = !writeCap || type === 'selector';

    let pluginImpl = plugin as UIRowListPlugin;
    let spreadsheet: DTable = pluginImpl.spreadsheet;
    let spreadsheetConfig: DTableConfig = pluginImpl.spreadsheet.getConfig();
    let permissionHelper: DTablePermissionsHelper = pluginImpl.spreadsheet.getPermissionHelper();

    let columnAutoCompleteMap = pluginImpl.spreadsheet._autoCompleteOptionMap;
    //TODO: review code
    let mapAutocompleteFields: Map<string, Map<any, any>> = new Map<string, Map<any, any>>();

    //TODO: need Review code
    for (const [key, _value] of columnAutoCompleteMap.entries()) {
      let array = key.split(":");
      let spreadsheetCode = array[0];
      let keyField = array[1];
      let searchParams = { "filters": [...sql.createSearchFilter()], "maxReturn": 500 };

      appContext.createHttpBackendCall("DTableService", "searchDynamicDTableRows", { code: spreadsheetCode, params: searchParams })
        .withSuccessData((data: any) => {
          let records = data ?? [];
          let mapValues: Map<any, any> = new Map<any, any>();
          for (let record of records) {
            let val = record[keyField];
            mapValues.set(val, record);
          }
          mapAutocompleteFields.set(key, mapValues);
        })
        .call()
    }

    const uiEditBBAutoCompleteNew = (
      field: grid.FieldConfig, dRecord: grid.DisplayRecord, tabIndex: number, focus: boolean,
      onInputChange: grid.OnInputChange) => {

      let mapValues: Map<any, any> = mapAutocompleteFields.get(field.name) ?? new Map<any, any>();
      let array = field.name.split(":");
      let spreadsheetCode = array[0];
      let fieldMap = spreadsheet._autoCompleteFieldMap.get(spreadsheetCode) ?? [];
      let autoCompleteOnInputChange = (bean: any, field: string, _selectOpt: any, oldVal: any, newVal: any) => {
        if (oldVal !== newVal) {
          console.log('call on input change', field, newVal);
          let newRec = mapValues.get(newVal);
          if (!newRec) return;
          if (autoCompleteColumnMap.get(field) === 'Metadata') {
            dRecord.record[field] = newVal;
          } else {
            for (let fieldModel of fieldMap) {
              dRecord.record[fieldModel.fieldName] = newRec[fieldModel.refFieldName];
            }
          }
          onInputChange(dRecord.record, field, oldVal, newVal);
          this.vgridContext.getVGrid().forceUpdateView();
        }
      }

      let options = [];
      if (mapValues) {
        for (const [key, _value] of mapValues.entries()) {
          if (key) options.push(key);
        }
      }
      return (
        <input.BBOptionAutoComplete autofocus={focus} tabIndex={tabIndex}
          bean={dRecord.record} field={field.name} allowUserInput={spreadsheet._columns[field.name].allowUserInput}
          options={options} onInputChange={autoCompleteOnInputChange} />
      )
    }

    //Build column, column group
    let fields: Array<grid.FieldConfig> = spreadsheet._fields;
    let autoCompleteColumnMap: Map<string, AutocompleteType> = spreadsheet._supportedAutoCompleteColumnMap;

    for (let i = 0; i < fields.length; i++) {
      let field = fields[i];
      if (!field.editor) continue;
      if (autoCompleteColumnMap.has(field.name)) {
        let type: AutocompleteType = autoCompleteColumnMap.get(field.name) ?? 'None';
        if (type === 'Spreadsheet' || type === 'Metadata') {
          field.editor.enable = true;
          field.editor.type = 'string';
          field.editor.renderCustom = (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
            let { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
            return uiEditBBAutoCompleteNew(fieldConfig, displayRecord, tabIndex, focus, onInputChange);
          }
        }
      }
      if (!enableEditor) field.editor.enable = false;
    }
    if (fields.length > 0) {
      //TODO: must be ensure field config : container -> fixed left, remove: false, not owner column
      fields[0].onClick = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => this.onSelect(dRecord);
    }

    let configId = `spreadsheet:${spreadsheetConfig.code} `;
    let config: grid.VGridConfig = {
      id: configId,
      title: spreadsheetConfig.label,
      record: {
        dataCellHeight: 30,
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          let rec = dRec.record;
          if (rec._rowHeight_ > 0) return rec._rowHeight_;
          return 30;
        },

        onchangeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord, deltaY: number) => {
          let rec = dRec.record;
          let currHeight = rec._rowHeight_;
          if (currHeight < 10) currHeight = grid.DEFAULT.row.height;
          rec._rowHeight_ = currHeight + deltaY;
          return true;
        },
        editor: {
          supportViewMode: ['table'],
          enable: enableEditor
        },
        control: {
          width: 40,
          items: [
            {
              name: 'add', hint: 'Clone Row', icon: FeatherIcon.Copy,
              customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let create = (_ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => {
                  let newRecord = atRecord.cloneRecord();
                  newRecord.id = null;
                  newRecord['ownerAccountId'] = SESSION.getAccountId();
                  newRecord['ownerLabel'] = SESSION.getAccountAcl().getFullName();
                  return newRecord;
                }
                let allowInsert = (_ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => {
                  let recState = atRecord.getRecordState(false);
                  if (!recState) return false;
                  return true;
                }
                return (<grid.WGridInsertRow className='px-1'
                  context={ctx} row={dRecord.row} createInsertRecord={create} allowInsert={allowInsert} />);
              },
            },
            {
              name: 'trash', hint: 'Delete', icon: FeatherIcon.Trash,
              customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let record: any = dRecord.record;
                if (!permissionHelper.canDeleteRow(record)) return;
                return (<grid.WGridMarkRowDeleted context={ctx} row={dRecord.row} />);
              }
            }
          ]
        },

        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          ...fields
        ],
        fieldGroups: spreadsheet._fieldGroups,
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES(
            [entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED], type == 'selector' || smallScreen),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(type === 'selector' || smallScreen, T("Add")),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(type == 'selector' || smallScreen, T("Del")),

          createVGridFavButton(configId),
        ],
        filters: [
          ...entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true)
        ]
      },
      footer: {
        page: {
          hide: type !== 'page',
          render: (ctx: grid.VGridContext) => { return (<UIRowListControl context={ctx} />); }
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT("Select Type", type)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
          },
        }
      }
    }

    if (spreadsheet.getGroupByColumns().length > 0) {
      let aggModel = new grid.AggregationDisplayModel(T('All'), false);
      if (permissionHelper.hasAllRowPermissions()) {
        let agg = new grid.ValueAggregation(T("Owner"), "ownerAccountId", true).withFieldGetter(rec => rec['ownerLabel']);
        aggModel.addAggregation(agg);
      }

      let groupByColumns = spreadsheet.getAccessColumns();
      for (let column of groupByColumns) {
        let agg: any;
        if (column.type === 'Date') {
          agg = new grid.DateValueAggregation(T(column.label), column.columnId, 'YYYY-MM-DD', false)
        } else {
          agg = new grid.ValueAggregation(T(column.label), column.columnId, false);
        }
        aggModel.addAggregation(agg);
      }

      config.view.availables['aggregation'] = {
        viewMode: 'aggregation', treeWidth: 250,
        createAggregationModel(_ctx: grid.VGridContext) {
          for (let agg of aggModel.aggregations) {
            let sumFunc = agg.aggFunctions.find(sel => sel.name === 'Total');
            if (!sumFunc) {
              sumFunc = new grid.SumAggregationFunction(`Total`, spreadsheet.getNumberedColumns(), true);
              agg.withAggFunction(sumFunc);
            }
            agg.withOnClick((bucket: grid.Bucket) => {
              let collapse = bucket.collapse;
              bucket.collapse = !collapse;
              _ctx.model.getDisplayRecordList().updateDisplayRecords();
              _ctx.vgrid?.forceUpdateView();
            });
          }
          return aggModel;
        }
      }
    }

    if (type == 'selector') {
      config.record.control = { width: 5, items: [] };
      config.toolbar.actions = [
        createVGridFavButton(configId),
      ],
        config.toolbar.filters = entity.DbEntityListConfigTool.TOOLBAR_FILTERS(false)
      config.record.editor = {
        supportViewMode: []
      }
    }

    if (spreadsheet.hasSummarySupport()) {
      let summaryModel = spreadsheet.getSummaryModel();

      config.record['summary'] = {
        dataCellHeight: summaryModel.cellHeight,
        render: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord, _viewMode: grid.ViewMode) => {
          if (dRecord.type == 'bucket') return <></>;
          if (dRecord.type == 'agg') {
            let html = (
              <div className='flex-hbox align-items-end justify-content-between'>
                TODO...
              </div>
            );
            return html;
          }

          let html = (
            <grid.SummaryCell className='flex-hbox' context={ctx} record={dRecord}>
              <grid.SummaryInfo
                context={ctx} labelField={summaryModel.fieldLabel}
                descField={summaryModel.fieldDesc} infoField={summaryModel.fieldInfo} record={dRecord} />
            </grid.SummaryCell>
          );
          return html;
        }
      }
    }

    return config;
  }

  onDefaultSelect(_dRecord: grid.DisplayRecord) { }

  onNewAction() {
    let { plugin } = this.props;
    let pluginImpl = plugin as UIRowListPlugin;
    let spreadsheetConfig = pluginImpl.spreadsheet.getConfig();
    let newRecord: any = {
      dtableId: spreadsheetConfig.id,
      ownerAccountId: SESSION.getAccountId(),
      ownerLabel: SESSION.getAccountAcl().getFullName()
    };

    this.onAddRecord(newRecord, (rec) => {
      grid.initRecordState(rec, 0).markNew();
      plugin.getListModel().addRecord(rec);
      this.sort(plugin.getListModel().getRecords());
      plugin.update(plugin.getListModel().getRecords());
      this.getVGridContext().getVGrid().forceUpdateView();
    });
  }

  onAddRecord(record: any, callback: (rec: any) => void) {
    callback(record);
  }

  onDeleteAction() {
    const { plugin } = this.props;
    let pluginImpl = plugin as UIRowListPlugin;
    let permissionHelper = pluginImpl.permissionHelper;
    const records: Array<any> = plugin.getListModel().getSelectedRecords();
    if (!permissionHelper.canDeleteAllRow(plugin.getListModel().getSelectedRecords())) {
      let message = "You don't have permission";
      bs.notificationShow("danger", message, <div className='alert alert-danger'>{message}</div>);
      return;
    }
    for (let i = 0; i < records.length; i++) {
      let record = records[i];
      grid.getRecordState(record).markDeleted();
    }
    plugin.getListModel().reset();
    this.getVGridContext().getVGrid().forceUpdateView(true);
  }

  changeStorageState(restPath: string, ctx: grid.VGridContext, newStorageState: string) {
    let { appContext, plugin } = this.props;
    const records: Array<any> = ctx.model.getSelectedRecords();
    let pluginImpl = plugin as UIRowListPlugin;
    let permissionHelper = pluginImpl.permissionHelper;
    if (!permissionHelper.canDeleteAllRow(records)) {
      let message = "You don't have permission";
      bs.notificationShow("danger", message, <div className='alert alert-danger'>{message}</div>);
      return;
    }
    let listModel = ctx.model;
    let ids = listModel.getSelectedRecordIds();
    let success = (_response: server.BackendResponse) => {
      appContext.addOSNotification("success", `Set Storage State Rows Success`);
      listModel.removeSelectedDisplayRecords();
      this.sort(plugin.getListModel().getRecords());
      plugin.update(plugin.getListModel().getRecords());
      ctx.getVGrid().forceUpdateView();
    };
    let fail = (response: server.BackendResponse) => {
      appContext.addOSNotification("danger", `Set Storage State Rows Failed`);
      console.error('Error');
      console.error(response);
    };
    let changeStorageStateReq = { entityIds: ids, newStorageState: newStorageState }
    let restClient = appContext.getServerContext().getRestClient();
    restClient.put(restPath, changeStorageStateReq, success, fail);
  }

  uploadId = util.common.IDTracker.next();

  onUploadSuccess = (resource: component.UploadResource, _modify: boolean) => {
    let { appContext, plugin } = this.props;
    let pluginImpl = plugin as UIRowListPlugin;
    let spreadsheet = pluginImpl.spreadsheet;
    let callback = (_response: server.BackendResponse) => {
      this.uploadId = util.common.IDTracker.next();
      this.reloadData();
    }
    appContext.DO_NOT_USE_serverPOST(`/dtable/${spreadsheet.getConfig().code}/upload`, resource, callback);
  }

  onDeleteUploadResourceSuccess(_resource: any) { }

}
