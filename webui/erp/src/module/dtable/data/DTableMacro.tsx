import { grid, sql, entity } from '@datatp-ui/lib';

import { DTableRestURL } from '../RestURL';
import { UIRowListPlugin } from './UIRowListPlugin';

import { RecordFormulas, SortUtils } from './RowListUtils';

import { VGridMacroContext } from '../vgrid/VGridMacro';

type CellParam = {
  columnId: string;
  value: any;
}

export class DTableMacroContext extends VGridMacroContext {
  uiRowList: entity.DbEntityList;

  constructor(uiList: entity.DbEntityList, gridContext: grid.VGridContext) {
    super(uiList, gridContext)
    this.uiRowList = uiList;
  }

  findColumnConfigs(columnNames: string[]) {
    let { plugin } = this.uiRowList.props;
    let pluginImpl = plugin as UIRowListPlugin;
    return pluginImpl.spreadsheet.findSpreadsheetColumnsByNames(columnNames);
  }

  getRowByCellStringValue(param: CellParam, callBack: (rec: any) => void) {
    let { appContext } = this.uiRowList.props;

    appContext.createHttpBackendCall("DTableService", "getDynamicDTableRowByCellStringValue", { param: param })
      .withSuccessData((data: any) => {
        callBack(data);
      })
      .call()

  }

  getRowById(rowId: number, callBack: (rec: any) => void) {
    let { appContext } = this.uiRowList.props;

    appContext.createHttpBackendCall("DTableService", "getDynamicDTableRowById", { rowId: rowId })
      .withSuccessData((data: any) => {
        callBack(data);
      }).call()
  }

  addRow(record: any) {
  }

  registerListenerHideColumns(...columns: string[]) {
    let listener = {
      onInitConfig: (context: grid.VGridContext, _config: grid.VGridConfig) => {
        for (let column of columns) {
          context.getVGridConfigModel().getRecordConfigModel().setVisibleFieldConfig(column, false);
        }
      },
    }
    this.gridContext.registerListener("hide-column", listener);
  }

  initGRID(GRID: any) {
    GRID.RecordFormulas = RecordFormulas;
    GRID.SortUtils = SortUtils;
  }
}