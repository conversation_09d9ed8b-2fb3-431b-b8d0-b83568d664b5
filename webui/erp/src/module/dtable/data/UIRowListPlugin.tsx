import { app, sql, bs, entity, grid } from '@datatp-ui/lib';

import { CellType, DTable, DTableColumn, DTableConfig } from '../config/DTableConfig';
import { DTablePermissionsHelper } from '../config/DTablePermissionHelper';

const SESSION = app.host.DATATP_HOST.session;

export class UIRowListPlugin extends entity.DbEntityListPlugin {
  spreadsheet: DTable;
  permissionHelper: DTablePermissionsHelper;

  constructor(context: grid.VGridContext, spreadsheet: DTableConfig) {
    super([]);
    this.spreadsheet = new DTable(context, spreadsheet, SESSION.getAccountId());
    this.permissionHelper = this.spreadsheet._permissionHelper;
    let rangerFilter: any[] = [];
    let searchableColumnMap: Map<CellType, Array<DTableColumn>> = this.spreadsheet.getSearchableColumnMap();
    if (searchableColumnMap.get('Date')) {
      let columns = searchableColumnMap.get('Date') as Array<DTableColumn>;
      for (let column of columns) {
        rangerFilter.push(...sql.createDateTimeFilter(column.columnId, column.label));
      }
    }

    this.backend = {
      context: 'company',
      service: 'DTableService',
      searchMethod: 'searchDynamicDTableRows',
      changeStorageStateMethod: 'updateDTableRowStorageState',
    }

    this.searchParams = {
      "params": {},
      "filters": [
        ...sql.createSearchFilter(),
      ],
      "optionFilters": [
        sql.createStorageStateFilter(
          [entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED]),
      ],
      "rangeFilters": [...rangerFilter],
      maxReturn: 10000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { code: this.spreadsheet.getConfig().code, params: this.searchParams }).call()
  }
}

class DateUtils {
  private static today: Date = new Date();
  private static firstDayOfMonth: Date = new Date(Date.UTC(this.today.getUTCFullYear(), this.today.getUTCMonth(), 1));

  public static getTodayDateParam(): string {
    return this.today.toLocaleDateString("en-GB", { year: "numeric", day: "2-digit", month: "2-digit" }) + "@00:00:00+0700";
  }

  public static getFirstDayOfMonth(): string {
    return this.firstDayOfMonth.toLocaleDateString("en-GB", { year: "numeric", day: "2-digit", month: "2-digit" }) + "@00:00:00+0700";
  }
}
