export const OKRRestURL = {
  project: {
    load: (id: number) => { return `/okr/project/${id}` },
    findChildrenProject: (id: number) => { return `/okr/project/${id}/find-children` },
    save: "/okr/project",
    changeRootProjectType: "/okr/project/change-type",
    changeRootProjectCycleType: "/okr/project/change-cycle-type",
    new: "/okr/project/new",
    createProjectTask: (id: number) => { return `/okr/project/${id}/create-project-task` },
    copyRoot: (id: number) => { return `/okr/project/${id}/copy-root-project` },
    copy: (id: number) => { return `/okr/project/${id}/copy` },
    report: (id: number) => { return `/okr/project/${id}/report` },
    search: "/okr/project/search",
    saveState: "/okr/project/storage-state",
  },

  keyResultMaster: {
    // Reporting
    keyMasterReport: (keyMasterId: number) => { return `/okr/key-result-master/report/${keyMasterId}` },
    keyMasterReportViaObjective: (objectiveId: number) => { return `/okr/key-result-master/report/objective/${objectiveId}` },
    keyMasterReportViaProject: (projectId: number) => { return `/okr/key-result-master/report/project/${projectId}` },
  },
};