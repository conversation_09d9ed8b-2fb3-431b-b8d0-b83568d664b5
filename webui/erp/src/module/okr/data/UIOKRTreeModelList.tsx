import React from 'react';
import * as FeatherIcon from 'react-feather';
import { util, grid, app, bs, entity, server } from '@datatp-ui/lib';

import { OKRRestURL, T } from '../Dependency';
import { UINewOKRKeyResultMaster, UIOKRKeyResultMasterEditor } from '../keyresultmaster/UIOKRKeyResultMasterEditor';
import { CycleType, ObjViewType, OKR_STRUCTURE_TYPE } from '../models';
import { UIOKRListUtils } from './UIOKRTreeModelListUtils';
import { UIOKRListControl, UIOKRListFooter } from './UIOKRTreeModelListControl';
import { OKRPermissionHelper } from '../project/OKRPermissionHelper';
import { UIOKRKeyActionList, UIOKRKeyActionListPlugin } from '../keyresultmaster/keyaction/UIOKRKeyActionList';
import { UIOKRTreeModelListSelector } from './UIOKRTreeModelListSelector';
import { UIOKRKeyResultMaster } from '../keyresultmaster/UIOKRKeyResultMaster';

const SESSION = app.host.DATATP_HOST.session;

export class UIOKRTreeModelListPlugin extends entity.DbEntityListPlugin {
  keyMaster: any | null;
  reportParam: {
    projectTask: any | null,
    project: any | null,
    projectId: number | null,
    objectiveId: number | null,
    objectiveViewType: ObjViewType,
    keyResultMasterId: number | null,
    cycleType: CycleType,
    includeType: OKR_STRUCTURE_TYPE[],
  };

  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'OKRService',
      searchMethod: 'searchOKRTreeModels'
    }
    this.reportParam = {
      projectTask: null, projectId: null, project: null, objectiveId: null, keyResultMasterId: null,
      includeType: ['OBJECTIVE', 'KEY_RESULT_MASTER'], objectiveViewType: ObjViewType.None,
      cycleType: CycleType.FullYear
    }
  }

  withIncludeTypes(includeTypes: OKR_STRUCTURE_TYPE[]) {
    this.reportParam.includeType = includeTypes;
    return this;
  }

  withOkrProject(project: any | null | undefined) {
    if (project) this.reportParam.project = project;
    if (project) this.reportParam.projectId = project.id;
    return this;
  }

  withProjectTask(projectTask: number | null | undefined) {
    if (projectTask) this.reportParam.projectTask = projectTask;
    return this;
  }

  withObjectiveViewType(objectiveViewType: ObjViewType) {
    if (objectiveViewType) this.reportParam.objectiveViewType = objectiveViewType;
    return this;
  }

  withCycleType(cycleType: CycleType | null | undefined) {
    if (cycleType) this.reportParam.cycleType = cycleType;
    return this;
  }

  withKeyMaster(keyMaster: any) {
    if (keyMaster.id) this.reportParam.keyResultMasterId = keyMaster.id;
    this.keyMaster = keyMaster;
    return this;
  }

  loadData(uiSource: entity.DbEntityList<any>) {
    this.createBackendSearch(uiSource, { reportParam: this.reportParam }).call();
  }
}

interface UIOKRTreeModelListProps extends entity.DbEntityListProps {
  permissionHelper: OKRPermissionHelper;
}
export class UIOKRTreeModelList extends entity.DbEntityList<UIOKRTreeModelListProps> {
  createVGridConfig(): grid.VGridConfig {
    const { appContext, pageContext, plugin, permissionHelper } = this.props;
    let pluginImpl = plugin as UIOKRTreeModelListPlugin;
    let { projectTask, project, keyResultMasterId, cycleType } = pluginImpl.reportParam;
    let configId = "okr:key-result-editor";
    let writeCap = permissionHelper.hasModeratorPermission();

    if (keyResultMasterId) {
      let isPIC = pluginImpl.keyMaster.mainContributorAccountId === permissionHelper.accessAccountId;
      writeCap = (permissionHelper.hasWritePermission() && isPIC) || permissionHelper.hasModeratorPermission()
    }


    const onInputChange = (
      ctx: grid.VGridContext, dRec: grid.DisplayRecord, _field: grid.FieldConfig, oldVal: any, newVal: any) => {
      if (oldVal !== newVal) {
        dRec.getRecordState().markModified();
        for (let rec of plugin.getRecords()) {
          if (rec['id'] == dRec.record['parentId']) {
            grid.getRecordState(rec).markModified();
          }
        }
        ctx.getVGrid().forceUpdateView();
      }
    }

    const onShowKeyActions = (keyMaster: any): void => {
      let keyActionPlugin = new UIOKRKeyActionListPlugin().withKeyResultMaster(keyMaster).withViewType('KEY_ACTION');
      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <UIOKRKeyActionList type={'page'} viewType={'KEY_ACTION'}
            appContext={appCtx} pageContext={pageCtx} hideControl
            plugin={keyActionPlugin} permissionHelper={permissionHelper} viewName={'tree'}
            newTaskTemplate={{ keyResultMasterId: keyMaster.keyResultMasterId, label: keyMaster.label, okrProjectId: keyMaster.projectId }}
            projectTask={projectTask} />
        );
      }
      pageContext.createPopupPage('okr-key-actions', T(`${keyMaster.label}: Key Actions`), createAppPage, { size: 'xl' })
    }

    let fieldGroups: Record<string, grid.FieldGroup> = {};

    let config: grid.VGridConfig = {
      id: configId,
      record: {
        dataCellHeight: 30,
        editor: {
          supportViewMode: writeCap ? ['table', 'tree'] : [],
          enable: !writeCap
        },
        control: {
          width: 100,
          items: UIOKRListControl.DEFAULT_ITEMS(permissionHelper),
          dnd: UIOKRListControl.DEFAULT_DND(this)
        },
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          ...UIOKRListUtils.DEFAULT_FIELDS(appContext, pageContext, pluginImpl, onInputChange, permissionHelper),
          ...UIOKRListUtils.KEY_ACTION_FIELD(projectTask, onShowKeyActions),
          {
            name: 'modifiedTime', label: 'Last Updated', state: { visible: true },
            width: 160, cssClass: 'text-right', format: util.text.formater.compactDateTime
          },
          ...UIOKRListUtils.CYCLE_FIELDS(cycleType, pluginImpl),
          ...entity.DbEntityListConfigTool.FIELDS(!pageContext.hasUserAdminCapability(), [...entity.DbEntityListConfigTool.FIELD_ENTITY]),
        ],
        fieldGroups: {
          ...fieldGroups
        },
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap || !project, {
            name: "add-objective", label: T('Objective'), icon: FeatherIcon.Plus,
            onClick: () => this.onAddObjective()
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap || !keyResultMasterId, {
            name: "add-keyResult", label: T('Key Result'), icon: FeatherIcon.Plus,
            createComponent: function (ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as UIOKRTreeModelList;
              return (
                <bs.Popover flex-hbox-grow-0 title={'Key Result'} closeOnTrigger=".btn">
                  <bs.PopoverToggle laf='primary' outline>
                    <FeatherIcon.List size={12} /> {'Key Result'}
                  </bs.PopoverToggle>
                  <bs.PopoverContent>
                    <div className='flex-vbox' style={{ width: 120 }}>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(1)}>
                        <FeatherIcon.Plus size={12} /> {T('January')}
                      </bs.Button>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(2)}>
                        <FeatherIcon.Plus size={12} /> {T('February')}
                      </bs.Button>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(3)}>
                        <FeatherIcon.Plus size={12} /> {T('March')}
                      </bs.Button>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(4)}>
                        <FeatherIcon.Plus size={12} /> {T('April')}
                      </bs.Button>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(5)}>
                        <FeatherIcon.Plus size={12} /> {T('May')}
                      </bs.Button>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(6)}>
                        <FeatherIcon.Plus size={12} /> {T('June')}
                      </bs.Button>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(7)}>
                        <FeatherIcon.Plus size={12} /> {T('July')}
                      </bs.Button>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(8)}>
                        <FeatherIcon.Plus size={12} /> {T('August')}
                      </bs.Button>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(9)}>
                        <FeatherIcon.Plus size={12} /> {T('September')}
                      </bs.Button>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(10)}>
                        <FeatherIcon.Plus size={12} /> {T('October')}
                      </bs.Button>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(11)}>
                        <FeatherIcon.Plus size={12} /> {T('November')}
                      </bs.Button>
                      <bs.Button laf='secondary' outline className="mb-1 py-1 ps-2 w-100 text-start" hidden={!writeCap}
                        onClick={() => uiRoot.onAddKeyResult(12)}>
                        <FeatherIcon.Plus size={12} /> {T('December')}
                      </bs.Button>
                    </div>
                  </bs.PopoverContent>
                </bs.Popover>
              )
            },
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, "Del"),
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('auto-refresh', T('Refresh')),
        ],
      },
      footer: {
        default: {
          render: (ctx: grid.VGridContext) => {
            return (<UIOKRListFooter context={ctx} />);
          }
        }
      },
      view: {
        currentViewName: 'tree',
        availables: {
          table: {
            viewMode: 'table'
          },
          tree: {
            viewMode: 'tree',
            label: 'Tree',
            icon: FeatherIcon.Folder,
            treeField: 'label',
            plugin: new OKRKeyResultTreeDisplayModelPlugin(pluginImpl.reportParam),
          },
        }
      },
    };

    if (pluginImpl.reportParam.keyResultMasterId) {
      config.toolbar.filters = entity.DbEntityListConfigTool.TOOLBAR_FILTERS(false);
    }

    if (!pluginImpl.reportParam.keyResultMasterId) {
      config.record.fieldGroups = {
        ...config.record.fieldGroups,
        'january': { label: 'January', fields: ['januaryVolume', 'januaryProgress'], visible: true },
        'february': { label: 'February', fields: ['februaryVolume', 'februaryProgress'], visible: true },
        'march': { label: 'March', fields: ['marchVolume', 'marchProgress'], visible: true },
        'april': { label: 'April', fields: ['aprilVolume', 'aprilProgress'], visible: true },
        'may': { label: 'May', fields: ['mayVolume', 'mayProgress'], visible: true },
        'june': { label: 'June', fields: ['juneVolume', 'juneProgress'], visible: true },
        'first-half-year': { label: 'First Half Year', fields: ['firstHalfYearVolume', 'firstHalfYearProgress'], visible: true },
        'july': { label: 'July', fields: ['julyVolume', 'julyProgress'], visible: true },
        'august': { label: 'August', fields: ['augustVolume', 'augustProgress'], visible: true },
        'september': { label: 'September', fields: ['septemberVolume', 'septemberProgress'], visible: true },
        'october': { label: 'October', fields: ['octoberVolume', 'octoberProgress'], visible: true },
        'november': { label: 'November', fields: ['novemberVolume', 'novemberProgress'], visible: true },
        'december': { label: 'December', fields: ['decemberVolume', 'decemberProgress'], visible: true },
        'second-half-year': { label: 'Second Half Year', fields: ['secondHalfYearVolume', 'secondHalfYearProgress'], visible: true }
      };
    }
    return config;
  }

  createKeyMasterFromSources = (item: any) => {
    const { appContext, pageContext, permissionHelper } = this.props;
    let onMultiSelect = (selectedBeans: Array<any>, pageCtx: app.PageContext) => {
      if (!selectedBeans || selectedBeans.length < 1) {
        let message = 'Need select Key Result Master';
        bs.notificationShow("warning", message, <div className='alert alert-warning'>{T(message)}</div>);
        return;
      }
      let keyMasterSourceIds: Array<any> = [];
      for (let row = 0; row < selectedBeans.length; row++) {
        let record = selectedBeans[row];
        if (record.type !== 'KEY_RESULT_MASTER') {
          let message = 'Select only Key Result Masters';
          bs.notificationShow("warning", message, <div className='alert alert-warning'>{T(message)}</div>);
          return;
        }
        if (item.type === 'KEY_RESULT_MASTER') {
          if (record.unit !== item.unit) {
            let message = 'Must be the same Unit';
            bs.notificationShow("warning", message, <div className='alert alert-warning'>{T(message)}</div>);
            return;
          }
          if (record.keyResultMasterId == item.keyResultMasterId) {
            let message = 'Must be select different item';
            bs.notificationShow("warning", message, <div className='alert alert-warning'>{T(message)}</div>);
            return;
          }
        }
        keyMasterSourceIds.push(record.keyResultMasterId);
      }

      let params = {
        rootObjectiveId: item.objectiveId,
        rootKeyMasterId: item.keyResultMasterId,
        groupName: '',
        keyMasterSourceIds: keyMasterSourceIds
      };
      if (item.type === 'GROUP') params.groupName = item.label;
      appContext
        .createHttpBackendCall('OKRService', 'createKeyMasterFromSources', { params: params })
        .withSuccessData((data: any) => {
          pageCtx.back();
          this.reloadData();
        })
        .withSuccessNotification("success", T("Create Key Result Master Success"))
        .withFailNotification("danger", T("Create Key Result Master Failed"))
        .call();
    }

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox'>
          <UIOKRTreeModelListSelector
            appContext={appCtx} pageContext={pageCtx} permissionHelper={permissionHelper}
            onMultiSelect={(_appContext, pageContext, selectedBeans) => onMultiSelect(selectedBeans, pageContext)} />
        </div>
      );
    }
    pageContext.createPopupPage("select-key-result-master", T("Key Result Master"), createAppPage, { size: "xl", backdrop: "static" });
  }

  onAddAction(newBean: any) {
    grid.initRecordState(newBean, 0).markModified();
    this.vgridContext.model.addRecord(newBean);
    this.vgridContext.getVGrid().forceUpdateView();
  }

  onAddObjective = () => {
    const { appContext, plugin } = this.props;
    let pluginImpl = plugin as UIOKRTreeModelListPlugin;
    let { project } = pluginImpl.reportParam;
    let newBean = { projectId: project.id, type: 'OBJECTIVE', label: 'New Objective', objectiveId: null, id: this.computeNewItemId() }
    appContext.createBackendCall('OKRService', 'newObjectiveTreeModel', { template: newBean })
      .withSuccessData((data: any) => {
        let newObjective = data;
        grid.initRecordState(newObjective, 0).markModified();
        this.vgridContext.model.addRecord(newObjective);
        this.vgridContext.getVGrid().forceUpdateView();
      })
      .call();
  }

  onAddKeyResultMaster(parent: any) {
    const { appContext, pageContext, plugin, permissionHelper } = this.props;
    let index = 1;
    if (plugin.getRecords()) plugin.getRecords().map(record => {
      if (record.type === "KEY_RESULT_MASTER" && record.objectiveId == parent.objectiveId && !grid.getRecordState(record).isMarkDeleted()) index += 1;
    });


    let newKeyResultMaster = {
      type: "KEY_RESULT_MASTER",
      parentId: parent.id,
      projectId: parent.projectId,
      objectiveId: parent.objectiveId,
      mainContributorAccountId: parent.mainContributorAccountId,
      mainContributorFullName: parent.mainContributorFullName,
      index: index,
      groupName: null,
      id: this.computeNewItemId()
    };
    if (parent.type === "GROUP") newKeyResultMaster.groupName = parent.groupName;

    appContext
      .createHttpBackendCall('OKRService', 'newKeyResultMaster', { model: newKeyResultMaster })
      .withSuccessData((data: any) => {
        let pluginImpl = plugin as UIOKRTreeModelListPlugin;
        let project = pluginImpl.reportParam.project;
        let createPopupContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

          let onPostCommit = (entity: any) => {
            pageCtx.back();
            this.reloadData();
          }
          return (
            <UINewOKRKeyResultMaster appContext={appCtx} pageContext={pageCtx}
              observer={new entity.ComplexBeanObserver(data)} permissionHelper={permissionHelper}
              groupNames={project.groupNames} onPostCommit={onPostCommit} />
          );
        }
        pageContext.createPopupPage('new-key-master', T(`New Key Result Master`), createPopupContent, { size: 'lg', backdrop: 'static' })

      })
      .call();
  }

  onAddKeyResult = (month: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12) => {
    let { appContext, plugin } = this.props;
    let pluginImpl = plugin as UIOKRTreeModelListPlugin;
    let { keyResultMasterId } = pluginImpl.reportParam;
    let dataCallback = (data: any) => {
      let newKeyResult = data;
      this.onAddAction(newKeyResult);
    }
    let targetDate = new Date();
    targetDate.setMonth(month - 1);
    targetDate.setHours(8, 0, 0);
    let newBean = {
      keyResultMasterId,
      type: 'KEY_RESULT',
      label: `Kết quả tháng ${month}`,
      keyResultId: null,
      targetDate: util.TimeUtil.javaCompactDateTimeFormat(targetDate),
      id: this.computeNewItemId()
    }
    appContext
      .createHttpBackendCall('OKRService', 'newKeyResultModel', { model: newBean })
      .withSuccessData(dataCallback)
      .call();
  }

  onDeleteRec = (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
    let rec = dRecord.record;
    const { plugin } = this.props;
    if (rec.type === 'GROUP' || rec.type === 'OBJECTIVE') {
      ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
      for (let child of dRecord.model.children) {
        let record = child.record;
        if (record['allocatedBy']) {
          let message = record['label'] + " is contributed to " + record['allocatedBy'];
          bs.notificationShow("warning", message, <div className='alert alert-warning'>{message}</div>);
          return;
        }
        let recordState = grid.getRecordState(record);
        recordState.toggleDeleted()
      }
    } else if (rec.type === 'KEY_RESULT_MASTER') {
      if (rec['allocatedBy']) {
        let callbackConfirm = () => {
          ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
          for (let record of plugin.getRecords()) {
            if (rec['parentId'] == record['id']) {
              record.collapse = false;
              grid.getRecordState(record).markModified();
            }
          }
          let records: Array<any> = this.computeKeyMasterIndexes(plugin.getRecords());
          plugin.getListModel().update(records);
          this.vgridContext.getVGrid().forceUpdateView();
        }
        let message = rec['label'] + " is contributed to " + rec['allocatedBy'];
        bs.dialogConfirmMessage('Delete', message, callbackConfirm);
      } else {
        ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
        for (let record of plugin.getRecords()) {
          if (rec['parentId'] == record['id']) {
            record.collapse = false;
            grid.getRecordState(record).markModified();
          }
        }
        let records: Array<any> = this.computeKeyMasterIndexes(plugin.getRecords());
        plugin.getListModel().update(records);
      }
    } else {
      ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
    }
    this.vgridContext.getVGrid().forceUpdateView();
  }

  onDeleteAction = () => {
    const { plugin } = this.props;
    let pluginImpl = plugin as UIOKRTreeModelListPlugin;
    if (plugin.getListModel().getSelectedRecords().length < 1) {
      let message = "No records are selected!";
      bs.notificationShow("warning", message, <div className='alert alert-warning'>{message}</div>);
      return;
    }
    let callbackConfirm = () => {
      let { keyResultMasterId } = pluginImpl.reportParam;
      let selectedRecs = plugin.getListModel().getSelectedRecords();
      for (let rec of selectedRecs) {
        if (rec.type === 'KEY_RESULT' && rec.keyResultMasterId != keyResultMasterId) {
          let message = "Action denied!";
          bs.notificationShow("warning", message, <div className='alert alert-warning'>{message}</div>);
          return;
        } else if (rec.type === 'GROUP' || rec.type === 'OBJECTIVE') {
          let records = plugin.getRecords();
          for (let record of records) {
            if (record.type === 'KEY_RESULT_MASTER' && record.parentId == rec.id) {
              if (record['allocatedBy']) {
                let message = record['label'] + " is contributed to " + record['allocatedBy'];
                bs.notificationShow("warning", message, <div className='alert alert-warning'>{message}</div>);
                return;
              }
              grid.getRecordState(record).toggleDeleted();
            }
          }
          grid.getRecordState(rec).toggleDeleted();
        } else {
          grid.getRecordState(rec).toggleDeleted();
          if (rec.type === 'KEY_RESULT_MASTER') {
            for (let record of plugin.getListModel().getRecords()) {
              if (record['type'] === 'OBJECTIVE' && rec['parentId'] == record['id']) {
                record.collapse = false;
                grid.getRecordState(record).markModified();
              }
            }
            let records: Array<any> = this.computeKeyMasterIndexes(plugin.getRecords());
            plugin.getListModel().update(records);
          }
        }
      }
      this.vgridContext.getVGrid().forceUpdateView();
    }
    let message = (<div className="text-danger">Do you want to delete these records?</div>);
    bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
  }

  onCopyObjective = (dRecord: grid.DisplayRecord) => {
    const { appContext } = this.props;
    let record = dRecord.record;
    let newBean = { ...record, objectiveId: null, id: this.computeNewItemId() };
    appContext.createBackendCall('OKRService', 'newObjectiveTreeModel', { template: newBean })
      .withSuccessData((data: any) => {
        let newObjective = data;
        newObjective.collapse = false;
        grid.initRecordState(newObjective, 0).markModified();
        this.vgridContext.model.addRecord(newObjective);

        let items = this.vgridContext.model.getRecords().filter(item => item['type'] !== 'OBJECTIVE' && item['objectiveId'] == record['objectiveId'])
        items.map(item => {
          let cloneItem = {
            ...item, objectiveId: newObjective.objectiveId, keyResultMasterId: null, parentId: newObjective.id,
            id: this.computeNewItemId(), progress: 0, currentValue: 0,
          };
          grid.initRecordState(cloneItem, 0).markModified();
          this.vgridContext.model.addRecord(cloneItem);
        })
        this.vgridContext.getVGrid().forceUpdateView();
      })
      .call();
  }

  onCopyKeyResultMaster = (dRecord: grid.DisplayRecord) => {
    const { appContext, pageContext, plugin, permissionHelper } = this.props;
    let keyMaster = dRecord.record;
    let index = 1;
    if (plugin.getRecords()) plugin.getRecords().map(record => {
      if (record.type === "KEY_RESULT_MASTER" && record.objectiveId == keyMaster.objectiveId && !grid.getRecordState(record).isMarkDeleted()) index += 1;
    });

    let cloneKeyMaster = {
      ...keyMaster, keyResultMasterId: null, id: this.computeNewItemId(), index: index,
      allocatedBy: null
    };

    appContext
      .createHttpBackendCall('OKRService', 'newKeyResultMaster', { model: cloneKeyMaster })
      .withSuccessData((data: any) => {
        let pluginImpl = plugin as UIOKRTreeModelListPlugin;
        let project = pluginImpl.reportParam.project;
        let createPopupContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

          let onPostCommit = (entity: any) => {
            pageCtx.back();
            this.reloadData();
          }
          return (
            <UINewOKRKeyResultMaster appContext={appCtx} pageContext={pageCtx}
              observer={new entity.ComplexBeanObserver(data)} permissionHelper={permissionHelper}
              groupNames={project.groupNames} onPostCommit={onPostCommit} />
          );
        }
        pageContext.createPopupPage('new-key-master', T(`New Key Result Master`), createPopupContent, { size: 'lg', backdrop: 'static' })

      })
      .call();
  }

  onCopyKeyResult = (dRecord: grid.DisplayRecord) => {
    let plugin = this.props.plugin as UIOKRTreeModelListPlugin;
    let item = dRecord.record;
    let cloneItem = {
      ...item, keyResultMasterId: plugin.reportParam.keyResultMasterId, keyResultId: null, id: this.computeNewItemId(),
      progress: 0, currentValue: 0,
    };
    this.onAddAction(cloneItem);
  }

  onShowKeyResultMasterConfig = (record: any) => {
    if (!record.keyResultMasterId) {
      let message = "Action denied!";
      bs.notificationShow("warning", message, <div className='alert alert-warning'>{message}</div>);
      return;
    }
    let { appContext, pageContext, readOnly, plugin, permissionHelper } = this.props;
    let pluginImpl = plugin as UIOKRTreeModelListPlugin;
    let project = pluginImpl.reportParam.project;
    let dataCallback = (data: any) => {
      let keyResultMaster = data;
      let onPostCommit = (_entity: any, uiEditor?: app.AppComponent) => {
        this.reloadData();
      }
      let createPopupContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <UIOKRKeyResultMasterEditor appContext={appCtx} pageContext={pageCtx}
            observer={new entity.ComplexBeanObserver(keyResultMaster)} permissionHelper={permissionHelper}
            groupNames={project.groupNames} onPostCommit={onPostCommit} />
        );
      }
      pageContext.createPopupPage('config-key-master', T(`Config Key Master: ${keyResultMaster.label}`), createPopupContent, { size: 'xl', backdrop: 'static' })
    }
    appContext
      .createHttpBackendCall('OKRService', 'getKeyResultMaster', { id: record.keyResultMasterId })
      .withSuccessData(dataCallback)
      .call();
  }

  computeKeyMasterIndexes = (records: Array<any>) => {
    let holder: Array<any> = []
    let mapRecords: Map<String, Array<any>> = new Map<String, Array<any>>();

    records.map(rec => {
      let recs = mapRecords.get(rec.parentId);
      if (!recs) mapRecords.set(rec.parentId, [rec]);
      else recs.push(rec);
    });

    for (let parentId of mapRecords.keys()) {
      let recs = mapRecords.get(parentId);
      let objectiveIndex = 1;
      let keyMasterIndex = 1;
      recs?.map(rec => {
        if (grid.getRecordState(rec).isMarkDeleted()) {
          rec.index = 0;
        } else if (rec.type === 'OBJECTIVE') {
          if (rec.index != objectiveIndex) grid.getRecordState(rec).markModified();
          rec.index = objectiveIndex;
          objectiveIndex++;
        } else if (rec.type === 'KEY_RESULT_MASTER') {
          if (rec.index != keyMasterIndex) grid.getRecordState(rec).markModified();
          rec.index = keyMasterIndex;
          keyMasterIndex++;
        }
        holder.push(rec);
      })
    }
    return holder;
  }

  computeNewItemId = () => {
    let records = this.vgridContext.model.getRecords();
    var uniqueIds = [...new Set(records.map(rec => rec.id))];
    return this.generateUniqueId(uniqueIds);
  }

  generateUniqueId = (ids: number[]): number => {
    var id = Math.floor(Math.random() * 100000);
    if (ids.includes(id)) {
      return this.generateUniqueId(ids);
    }
    return id;
  }

  updateAfterDrop = (_vgridCtx: grid.VGridContext, records: Array<any>) => {
    const { plugin } = this.props;
    plugin.getListModel().update(records);
    this.vgridContext.getVGrid().forceUpdateView(true);
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    let { appContext, pageContext, readOnly, plugin, permissionHelper } = this.props;
    let pluginImpl = plugin as UIOKRTreeModelListPlugin;
    let record = dRecord.record;

    if (record.keyResultMasterId && record.type === 'KEY_RESULT_MASTER' || (record.type === 'KEY_RESULT' && record.keyResultMasterId !== pluginImpl.reportParam.keyResultMasterId)) {
      let dataCallback = (data: any) => {
        let keyMaster = data;
        if (pluginImpl.reportParam.keyResultMasterId) {
          let createPopupContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (
              <UIOKRKeyResultMaster appContext={appCtx} pageContext={pageCtx} readOnly={true}
                observer={new entity.ComplexBeanObserver(keyMaster)} permissionHelper={permissionHelper} />
            );
          }
          pageContext.createPopupPage('key-result-master-contribute', T(`Contribute ${keyMaster.label}`), createPopupContent, { size: 'xl', backdrop: 'static' })
        } else {
          let createAppPage = () => {
            return (
              <UIOKRKeyResultMaster appContext={appContext} pageContext={pageContext} readOnly={readOnly}
                observer={new entity.ComplexBeanObserver(keyMaster)} permissionHelper={permissionHelper} />
            );
          }
          this.vgridContext.getVGrid().addTab(`Key Result Master: ${keyMaster.label}`, createAppPage);
          this.vgridContext.getVGrid().forceUpdateView();
        }
      }
      appContext
        .createHttpBackendCall('OKRService', 'getKeyResultMaster', { id: record.keyResultMasterId })
        .withSuccessData(dataCallback)
        .call();
    }
  }

  render() {
    if (this.isLoading()) return this.renderLoading();
    const { pageContext, plugin, permissionHelper } = this.props;
    let pluginImpl = plugin as UIOKRTreeModelListPlugin;
    if (!pluginImpl.reportParam.keyResultMasterId) {
      let holder: Array<any> = [];

      for (let i = 0; i < plugin.getRecords().length; i++) {
        let record = plugin.getRecords()[i];
        if (record.type !== 'KEY_RESULT_MASTER') {
          holder.push(record);
          continue;
        }

        if (record.mainContributorAccountId === SESSION.getAccountId()) {
          holder.push(record);
          continue;
        }

        let minAccessCapability: app.AppCapability = new app.AppCapability(record.minAccessCapability);
        if (pageContext.hasUserAdminCapability() || minAccessCapability.capability === 'None') {
          holder.push(record);
          continue;
        }

        let permission = permissionHelper.permissionMap.get(SESSION.getAccountId());
        if (!permission) continue;

        let capability: app.AppCapability = new app.AppCapability(permission.capability);
        if (capability.hasCapability(minAccessCapability)) holder.push(record);
      }
      this.getVGridContext().model.records = holder;
      plugin.update(holder);
    }

    return this.renderUIGrid();
  }
}

class OKRKeyResultTreeDisplayModelPlugin extends grid.TreeDisplayModelPlugin {
  reportParam: any;

  constructor(reportParam: any) {
    super();
    this.reportParam = reportParam;
  }

  buildTreeRecords(records: Array<any>): Array<grid.TreeRecord> {
    let treeRecordMap = new util.RecordMap<grid.TreeRecord>();
    let rootTreeRecords = new Array<grid.TreeRecord>();
    let remainingHolder = new Array<grid.TreeRecord>();
    for (let record of records) {
      if (this.isRootRecord(record)) {
        let treeRecord = new grid.TreeRecord(record, 0);
        rootTreeRecords.push(treeRecord);
        treeRecordMap.putBy(this.getId(record), treeRecord);
      } else {
        remainingHolder.push(record);
      }
    }
    this.buildRemaining(treeRecordMap, remainingHolder);

    for (let treeRecord of treeRecordMap.getAll()) {
      treeRecord.collapse = false;
    }
    return rootTreeRecords;
  }
}
