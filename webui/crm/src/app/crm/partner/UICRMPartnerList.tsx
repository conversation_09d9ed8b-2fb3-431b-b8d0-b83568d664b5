import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, grid, sql, app, entity, input, util } from '@datatp-ui/lib';

import { UIBFSOneCustomer, UINewBFSOnePartnerEditor } from './UIBFSOnePartner';
import { T } from '../price';
import { PartnerType } from './BBRefBFSOneCustomer';
import { buildTooltipValues, WRateFinderGridFilter } from '../price';
import { WCheckPartnerTaxCode } from './WCheckPartnerTaxCode';

const SESSION = app.host.DATATP_HOST.session;

export type Space = 'User' | 'Company' | 'System'

export class UIBFSOneCustomerListPlugin extends entity.DbEntityListPlugin {
  space: Space;

  constructor(type: PartnerType = 'Customer', space: Space = 'User') {
    super([]);
    //TODO: Dan - fix it after implement permission in sales.
    const accessAllAgentLoginIds: string[] = ['tessie.vnsgn', 'dan', 'leo.vnsgn'];
    const loginId: string = SESSION.getLoginId();

    if (type === 'Agent' && accessAllAgentLoginIds.includes(loginId)) {
      space = 'System';
    }

    this.space = space;

    let group: string = 'CUSTOMERS';
    if (type === 'Agent') group = 'AGENTS'
    else if (type === 'Coloader') group = 'COLOADERS';

    this.backend = {
      context: 'company',
      service: 'BFSOnePartnerService',
      searchMethod: 'searchBFSOnePartners',
      deleteMethod: '',
      changeStorageStateMethod: '',
      entityLabel: 'Customer'
    }

    this.searchParams = {
      "params": {
        group: group,
        space: space,
      },
      "filters": [...sql.createSearchFilter()],
      "maxReturn": 5000
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { param: this.searchParams }).call();
  }

}

export class UIBFSOnePartnerCustomerListPlugin extends entity.DbEntityListPlugin {
  space: Space;

  constructor(type: PartnerType = 'Customer', space: Space = 'User') {
    super([]);
    this.space = space;
    let group: string = 'CUSTOMERS';
    if (type === 'Agent') group = 'AGENTS'
    else if (type === 'Coloader') group = 'COLOADERS';

    this.backend = {
      context: 'company',
      service: 'BFSOnePartnerService',
      searchMethod: 'searchBFSOnePartnerCustomers',
      deleteMethod: '',
      changeStorageStateMethod: '',
      entityLabel: 'Customer'
    }

    this.searchParams = {
      "params": {
        group: group,
        space: space,
      },
      "filters": [...sql.createSearchFilter()],
      "maxReturn": 5000
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { param: this.searchParams }).call();
  }

}


export interface UIBFSOnePartnerListProps extends entity.DbEntityListProps {
  partnerType: PartnerType
}
export class UIBFSOnePartnerList extends entity.DbEntityList<UIBFSOnePartnerListProps> {

  createVGridConfig() {
    let { type, partnerType } = this.props;

    const renderTooltipAdvanced = (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
      const record = dRecord.record;
      const val = record[field.name] || '-';

      // Build tooltip content from record fields
      const tooltipFields = [
        { key: 'investmentOrigin', label: 'Investment Origin' },
        { key: 'kcnLabel', label: 'KCN' },
        { key: 'address', label: 'Address' },
        { key: 'note', label: 'Note' },
      ];

      // Build tooltip content from record fields
      const { htmlFormat, textFormat } = buildTooltipValues(record, tooltipFields);

      const handleClick = () => {
        navigator.clipboard.writeText(textFormat);
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      };

      let offsetX = field.width || 120

      return (
        <bs.CssTooltip width={400} offset={{ x: offsetX, y: 0 }}>
          <bs.CssTooltipToggle >
            <div className='flex-hbox' onClick={handleClick}>
              {field.fieldDataGetter ? field.fieldDataGetter(record) : field.format ? field.format(val) : val}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent>
            {htmlFormat}
          </bs.CssTooltipContent>
        </bs.CssTooltip>
      );
    }

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          // entity.DbEntityListConfigTool.FIELD_ON_SELECT('bfsonePartnerCode', T('Code'), 120, 'fixed-left'),
          {
            name: 'bfsonePartnerCode', label: T('Code.'), width: 100, filterable: true,
            container: 'fixed-left',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record: any = dRecord.record;
              let val = record[_field.name] || 'N/A';
              let uiRoot = ctx.uiRoot as UIBFSOnePartnerList
              return (
                <div className="flex-vbox flex-grow-0 justify-content-end align-items-center">
                  <bs.Button laf='link' onClick={() => uiRoot.onSelect(dRecord)}>
                    {val}
                  </bs.Button>
                </div>
              )
            }
          },
          {
            name: 'dateCreated', label: T('Date Created'), width: 140,
            filterable: true, filterableType: 'date', format: util.text.formater.compactDate,
            fieldDataGetter(record) {
              let val = record['dateCreated'] || record['createdTime']
              if (val) return util.text.formater.compactDate(val);
              return 'N/A'
            },
          },
          {
            name: 'name', label: 'Name', width: 300, cssClass: 'px-1', filterable: true,
            customRender: renderTooltipAdvanced
          },
          { name: 'label', label: 'Label', width: 250, cssClass: 'px-1', state: { visible: false } },
          { name: 'personalContact', label: 'Personal Contact', state: { visible: false }, width: 200 },
          {
            name: 'taxCode', label: 'Tax Code', width: 150, filterable: true,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'address', label: 'Address', width: 300,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'investmentOrigin', label: 'Investment Origin', width: 150,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'note', label: 'Note', width: 350,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'modifiedTime', label: T('Modified Time'), width: 170,
            filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime,
            customRender: renderTooltipAdvanced
          },
        ]
      },
      toolbar: {
        hide: true,
      },
      footer: {
        default: {
          hide: type === 'selector',
          render: (ctx: grid.VGridContext) => {
            let uiList = ctx.uiRoot as UIBFSOnePartnerList;
            let { appContext, pageContext } = uiList.props;
            let writeCap = pageContext.hasUserWriteCapability();

            return (
              <bs.Toolbar className='border' hide={!writeCap}>
                <entity.WButtonEntityNew appContext={appContext} pageContext={pageContext}
                  label={`New ${partnerType}`} onClick={this.onNew} />
              </bs.Toolbar>
            );
          }
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T('Select'), type),
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let customerPartner = dRecord.record;
    let { appContext, pageContext, partnerType } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let param: any = { id: customerPartner.id };

    appContext
      .createHttpBackendCall("BFSOnePartnerService", "getBFSOnePartner", param)
      .withSuccessData((data: any) => {
        let observer = new entity.ComplexBeanObserver(data);

        const onPostCommit = (_entity: any, uiEditor?: app.AppComponent) => {
          this.reloadData();
          uiEditor?.props.pageContext.back();
        }

        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (<UINewBFSOnePartnerEditor appContext={appCtx} pageContext={pageCtx} observer={observer} readOnly={!writeCap}
            onPostCommit={onPostCommit} partnerType={partnerType} />);
        };
        let popupLabel: string = `${partnerType}: ${customerPartner.name}`;
        pageContext.createPopupPage(`fwd-customer-${util.IDTracker.next()}`, popupLabel, createAppPage, { size: 'flex-lg', backdrop: 'static' });
      })
      .call()
  }

  onNew = () => {
    let { pageContext, partnerType } = this.props;
    let observer = new entity.ComplexBeanObserver({});

    const pageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox' style={{ minHeight: 600 }}>
          <UINewBFSOnePartnerEditor
            appContext={appCtx} pageContext={pageCtx} observer={observer} partnerType={partnerType}
          // onPostCommit={(_bean) => {
          //   pageCtx.back();
          //   this.onAddOrModifyDBRecordCallback(_bean);
          //   this.getVGridContext().getVGrid().forceUpdateView();
          // }}
          />
        </div>
      )
    }
    pageContext.createPopupPage('new-partner', `New ${partnerType}`, pageContent, { size: 'flex-lg', backdrop: 'static' });
  }

  onDeleteAction(): void {
    const { appContext, plugin } = this.props;
    const selectedIds = plugin.getListModel().getSelectedRecordIds();

    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("warning"), T("No Customer Selected!"));
      return;
    }

    appContext.createHttpBackendCall('BFSOnePartnerService', 'deleteBFSOnePartners', { targetIds: selectedIds })
      .withSuccessData((_data: any) => {
        this.reloadData();
      })
      .withEntityOpNotification('delete', 'Partner')
      .call();
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    plugin.searchParams = searchParam;
    this.reloadData();
    this.nextViewId();
    // this.forceUpdate();
    this.vgridContext.getVGrid().forceUpdateView();
  };

  render() {
    if (this.isLoading()) return this.renderLoading();
    const { appContext, pageContext } = this.props;

    return (
      <div className='flex-vbox'>

        <div className="bg-white flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

          <div className="flex-hbox justify-content-start align-items-center" >
            <WCheckPartnerTaxCode appContext={appContext} pageContext={pageContext} className='mx-0' />
          </div>

          <div className="flex-hbox justify-content-end align-items-center gap-1" >
            <WRateFinderGridFilter context={this.vgridContext} />

            {pageContext.hasUserModeratorCapability() ?
              <div className="flex-hbox justify-content-end align-items-center flex-grow-0">
                <bs.Button laf='warning' className="border-0 p-1 border-end rounded-0" outline
                  onClick={() => this.onDeleteAction()}>
                  <FeatherIcon.Trash size={12} /> Del
                </bs.Button>
              </div>
              : null
            }

          </div>

        </div>

        <div key={this.viewId} className='flex-vbox'>
          {this.renderUIGrid()}
        </div>

      </div>
    )
  }
}

