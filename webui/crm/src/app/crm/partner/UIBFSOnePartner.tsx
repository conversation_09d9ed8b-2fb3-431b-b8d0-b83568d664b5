import React from 'react'
import * as FeatherIcon from "react-feather";
import { bs, input, entity, app, server, util } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { T } from '../price';
import { PartnerType } from './BBRefBFSOneCustomer';

import BBRefLocation = module.settings.BBRefLocation;

//TODO: Dan - more to resource entity
export const SOURCE_OPTIONS: string[] = [
  "WCA", "WPA", "GFFG", "CLC Projects",
  "A2B (not a member of associations in this list, Agent has one-way nominations to Bee)",
  "B2A (not a member of associations in this list, <PERSON> has one-way nominations to Agent)",
  "A2B2A (not a member of associations in this list, Agent and Bee have reciprocal nominations)",
  "FREEHAND", "BEE", "MLN (Millenium Logistics Network)",
  "FREIGHT MIDPOINT", "LOGNET", "GAA", "PCN", "CFN",
  "EAA", "DCS", "ISS", "FCUBE", "TALA",
  "FPS - FAMOUS PACIFIC SHIPPING", "JOINT SALES", "PANGEA NETWORK"
]

import BBRefCountry = module.settings.BBRefCountry;
import BBRefState = module.settings.BBRefState;
import { UIPartnerEventHistoryList, UIPartnerEventHistoryListPlugin } from '../sales/leads/UIPartnerEventHistoryList';

function validatePartner(partner: any) {
  let agentCategories: string[] = ['AGENT_DOMESTIC', 'AGENT_OVERSEAS']

  if (agentCategories.includes(partner['category'])) return;
  let missingFields: string[] = [];

  if (!partner['label'] || !partner['localizedLabel']) missingFields.push('Partner Name (En/Vn)');
  if (!partner['address'] || !partner['localizedAddress']) missingFields.push('Address (En/Vn)');
  if (partner['category'] != 'AGENT_DOMESTIC' && partner['category'] != 'AGENT_OVERSEAS' && !partner['provinceId']) missingFields.push('Province');
  if (!partner['countryId']) missingFields.push('Country');
  if (!partner['personalContact']) missingFields.push('Personal Contact');
  if (!partner['cell']) missingFields.push('Cell Phone');
  if (!partner['email']) missingFields.push('Email');
  if (!partner['industryCode'] || !partner['industryLabel']) missingFields.push('Industry');

  if (missingFields.length > 0) {
    bs.dialogShow('Missing Information',
      <div className="text-danger fw-bold text-center py-3 border-bottom">
        <FeatherIcon.AlertCircle className="mx-2" />
        {`Please provide: ${missingFields.join(', ')}.`}
      </div>,
      { backdrop: 'static', size: 'sm' }
    );
    throw new Error(`Please provide: ${missingFields.join(', ')}.`);
  }
}

export interface UIBFSOnePartnerProps extends entity.AppComplexEntityEditorProps {
  partnerType: PartnerType,
}

export class UIBFSOneCustomer extends entity.AppDbComplexEntityEditor<UIBFSOnePartnerProps> {

  onPreCommit = (observer: entity.ComplexBeanObserver) => {
    let partner = observer.getMutableBean();
    validatePartner(partner);
  }

  render(): React.ReactNode {
    let { appContext, pageContext, observer, partnerType } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let partner = observer.getMutableBean();
    const referencePartnerType = 'bfsone_partner';
    return (
      <div className='flex-vbox'>

        <div className="flex-vbox flex-grow-0 rounded bg-white px-2 py-1">
          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='bfsonePartnerCode' label={T("Partner No.")} disable />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='taxCode' label={T("Tax code.")} disable />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField bean={partner} field='name' label={T("Name")} disable={!writeCap} />
            </bs.Col>
          </bs.Row>
        </div>

        <div className='flex-vbox' style={{ flex: 1, overflow: 'auto' }} key={this.viewId}>
          <bs.TabPane>

            <bs.Tab name='partner-detail' label={T('Detail')} active>
              <div className="flex-vbox">

                <bs.Row>
                  <bs.Col span={6}>
                    <input.BBStringField
                      bean={partner} field='label' label={T("Partner Name (En)")} disable={!writeCap} required />
                  </bs.Col>

                  <bs.Col span={6}>
                    <input.BBStringField
                      bean={partner} field='localizedLabel' label={T("Partner Name (VN)")} disable={!writeCap} required />
                  </bs.Col>
                </bs.Row>

                <bs.Row>
                  <bs.Col span={3}>
                    <BBRefLocation required label='KCN'
                      appContext={appContext} pageContext={pageContext} bean={partner}
                      beanIdField={'kcnCode'} beanLabelField={'kcnLabel'} placeholder='KCN' hideMoreInfo
                      disable={!writeCap} inputObserver={observer} refLocationBy='id' locationTypes={['KCN']}
                      beanRefLabelField='label'
                      onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                        bean['countryId'] = selectOpt['countryId'];
                        bean['countryLabel'] = selectOpt['countryLabel'];
                        bean['provinceId'] = selectOpt['stateId'];
                        bean['provinceLabel'] = selectOpt['stateLabel'];
                        this.forceUpdate();
                      }} />
                  </bs.Col>
                  <bs.Col span={3}>
                    <BBRefCountry key={partner.kcnCode}
                      appContext={appContext} pageContext={pageContext}
                      placement="bottom-start" offset={[0, 5]} minWidth={350}
                      disable={!writeCap} label={T('Country')} placeholder="Enter Country"
                      required bean={partner} beanIdField={'countryId'} hideMoreInfo
                      beanLabelField={'countryLabel'} refCountryBy='id'
                      onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                        bean['countryId'] = selectOpt['id'];
                        bean['countryLabel'] = selectOpt['label'];
                        this.forceUpdate();
                      }}
                    />
                  </bs.Col>

                  <bs.Col span={3}>
                    <BBRefState key={partner.kcnCode}
                      appContext={appContext} pageContext={pageContext}
                      placement="bottom-start" offset={[0, 5]} minWidth={350}
                      disable={!writeCap} label={T('Province')} placeholder="Enter Province"
                      required bean={partner} beanIdField={'provinceId'} hideMoreInfo
                      beanLabelField={'provinceLabel'} countryId={partner.countryId}
                    />
                  </bs.Col>
                  <bs.Col span={3}>
                    <input.BBStringField bean={partner} label={T('Investment Origin')} field="investmentOrigin" disable={!writeCap} />
                  </bs.Col>
                </bs.Row>

                <bs.Row>
                  <bs.Col span={6}>
                    <input.BBTextField
                      bean={partner} label={T('Address (En)')} field="address" disable={!writeCap} required
                      style={{ height: '4em' }} />
                  </bs.Col>
                  <bs.Col span={6}>
                    <input.BBTextField
                      bean={partner} label={T('Address (Vn)')} field="localizedAddress" disable={!writeCap} required
                      style={{ height: '4em' }} />
                  </bs.Col>
                </bs.Row>

                <bs.Row>
                  <bs.Col span={3}>
                    <input.BBStringField
                      bean={partner} field='personalContact' label={T("Personal Contact")} disable={!writeCap} required />
                  </bs.Col>
                  <bs.Col span={3}>
                    <input.BBStringField
                      bean={partner} field='cell' label={T("Cell Phone")} disable={!writeCap} required />
                  </bs.Col>
                  <bs.Col span={3}>
                    <input.BBStringField
                      bean={partner} field='fax' label={T("FAX.")} disable={!writeCap} />
                  </bs.Col>
                  <bs.Col span={3}>
                    <input.BBStringField
                      bean={partner} field='email' label={T("Email")} disable={!writeCap} required validators={[util.validator.EMPTY_VALIDATOR]} />
                  </bs.Col>
                </bs.Row>

                <bs.Row>
                  <bs.Col span={3}>
                    <input.BBSelectField bean={partner} field="source" label={T('Source')}
                      options={SOURCE_OPTIONS} disable={!writeCap} />
                  </bs.Col>

                  <bs.Col span={3}>
                    <input.BBStringField
                      bean={partner} field='swiftCode' label={T("Swift Code.")} disable={!writeCap} />
                  </bs.Col>
                  <bs.Col span={3}>
                    <input.BBStringField
                      bean={partner} field='bankName' label={T("Bank Name.")} disable={!writeCap} />
                  </bs.Col>

                  <bs.Col span={3}>
                    <input.BBStringField
                      bean={partner} field='bankAddress' label={T("Bank Address.")} disable={!writeCap} />
                  </bs.Col>
                </bs.Row>

                <bs.Row>
                  <bs.Col span={6}>
                    <module.resource.BBRefResource
                      appContext={appContext} pageContext={pageContext}
                      placement="bottom-start" offset={[0, 5]} minWidth={350}
                      disable={!writeCap} label={T('Industry')} placeholder="Enter Industry"
                      required bean={partner} beanIdField={'industryCode'} hideMoreInfo
                      beanLabelField={'industryLabel'} resourceType={"industry"} refResourceBy="identifier" />
                  </bs.Col>
                  <bs.Col span={3}>
                    <input.BBStringField
                      bean={partner} field='routing' label={T("Route")} disable={!writeCap} placeholder='Luồng tuyến...'
                    />
                  </bs.Col>
                  <bs.Col span={3}>
                    <input.BBSelectField
                      bean={partner} field='scope' label={T("Location")} disable={!writeCap || !observer.isNewBean()}
                      options={['Domestic', 'Overseas']}
                    />
                  </bs.Col>
                </bs.Row>

                <input.BBTextField
                  bean={partner} label={T('Note')} field="note" disable={!writeCap}
                  style={{ height: '8em', fontSize: '1rem' }} />

                <bs.Toolbar className='border'>

                  <entity.ButtonEntityCommit
                    appContext={appContext} pageContext={pageContext}
                    observer={observer} hide={!writeCap || observer.isNewBean()}
                    commit={{
                      entityLabel: partnerType, context: 'company',
                      service: "BFSOnePartnerService", commitMethod: "saveBFSOnePartner"
                    }}
                    onPreCommit={this.onPreCommit}
                    onPostCommit={this.onPostCommit}
                  />
                </bs.Toolbar>
              </div>
            </bs.Tab>

            <bs.Tab name='partner-history' label={T('Event History')}>
              <UIPartnerEventHistoryList
                appContext={appContext} pageContext={pageContext}
                plugin={new UIPartnerEventHistoryListPlugin().withReferencePartner(partner.id, referencePartnerType)}
                referencePartnerId={partner.id} referencePartnerType={referencePartnerType}
              />
            </bs.Tab>
          </bs.TabPane>
        </div>
      </div>
    )
  }
}

export class UINewBFSOnePartnerEditor extends entity.AppDbComplexEntityEditor<UIBFSOnePartnerProps> {
  state = {
    isSending: false
  };

  categories: string[] = [];
  partnerPattern: string = '';

  constructor(props: UIBFSOnePartnerProps) {
    super(props);
    this.partnerPattern = '';

    const { partnerType } = this.props;
    if (partnerType === 'Customer') {
      this.categories = ['CUSTOMER', 'COLOADER', 'SHIPPER', 'CONSIGNEE']
    } else if (partnerType === 'Agent') {
      this.categories = ['AGENT_DOMESTIC', 'AGENT_OVERSEAS']
    } else if (partnerType === 'Coloader') {
      this.categories = ['TRUCKER', 'CARRIER', 'AIRLINE', 'FORWARDER', 'OTHER']
    } else {
      this.categories = ['OTHER']
    }
  }

  onCheckTaxCode = (_wInput: input.WInput, _bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { appContext } = this.props;
    appContext.createHttpBackendCall('BFSOnePartnerService', 'findByTaxCode', { taxCode: newVal })
      .withSuccessData((partnerList: any[]) => {
        if (partnerList.length > 0) {
          let message = (
            <div className="ms-1 text-warning py-3 border-bottom">
              A partner with the tax code "{newVal}" already exists in the system.
            </div>
          );
          bs.dialogShow('Invalid Tax Code', message, { backdrop: 'static', size: 'sm' });
        }
      })
      .call();
  }

  requestToBFSOne = () => {
    const { appContext, observer } = this.props;
    this.onPreCommit(observer);

    let param: any = { partner: observer.getMutableBean() };
    let apiCommit: { service: string, commitMethod: string } = {
      service: 'BFSOneCRMService', commitMethod: 'createBFSOnePartner'
    }
    appContext.createHttpBackendCall(apiCommit.service, apiCommit.commitMethod, param)
      .withSuccessData((forwarderPartner: any) => {
        observer.setMutableBean(forwarderPartner)
        this.forceUpdate();

        appContext.addOSNotification("success", T(`Create BFSOne Partner Success!`));
        if (!forwarderPartner.bfsonePartnerCode) {
          appContext.addOSNotification("danger", T(`Request To BFSOne Fail!`));
        }
      })
      .withEntityOpNotification('commit', "Request to BFSOne")
      .withFail((response: server.BackendResponse) => {
        let title = T('Customer');
        let message = response.error.message || '';
        bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        return;
      })
      .call();
  }

  onPreCommit = (observer: entity.ComplexBeanObserver) => {
    let partner = observer.getMutableBean();
    validatePartner(partner);
    this.setState({ isSending: true });
  }

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    this.nextViewId();
    this.setState({ isSending: false });
    if (onPostCommit) {
      onPostCommit(entity, this);
    } else {
      this.forceUpdate();
    }
  }

  onChangeBFSOnePartnerCode = (_bean: any, field: string, oldVal: any, newVal: any) => {
    if (!newVal) return;
    this.partnerPattern = newVal;
    let { appContext, observer } = this.props;
    appContext.addOSNotification('info', "Fetch Partner Information in the system...");
    let param: any = { bfsonePartnerCode: newVal };
    appContext.createHttpBackendCall("BFSOnePartnerService", "syncBFSOnePartnersByCode", param)
      .withSuccessData((data: any) => {
        if (!data || Object.keys(data).length === 0) return;
        appContext.addOSNotification('success', "Permission updated and partner refreshed.");
        observer.replaceWith(data);
        this.nextViewId();
        this.forceUpdate();
      })
      .call()
  }

  onUpdateSimilarFields = (bean: any, field: string, _oldVal: any, newVal: any) => {
    bean[field] = newVal;
    if (field === 'name') {
      if (!bean['label'] || bean['label'].length == 0) bean['label'] = newVal;
      if (!bean['localizedLabel'] || bean['localizedLabel'].length == 0) bean['localizedLabel'] = newVal;
    }
    if (field === 'address') {
      if (!bean['localizedAddress'] || bean['localizedAddress'].length == 0) bean['localizedAddress'] = newVal;
    }
    this.forceUpdate();
  }

  render() {
    let { appContext, pageContext, observer, readOnly, partnerType } = this.props;
    let partner = observer.getMutableBean();
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly
    let title = partnerType ? partnerType : 'Customer Lead';

    return (
      <div className="flex-vbox">

        <div className='flex-vbox shadow-sm rounded h-100 bg-white'>

          <bs.Row className='pb-2'>
            <bs.Col span={12}>
              <bs.CssTooltip position='bottom-left' width={400} offset={{ x: 200, y: -12 }}>
                <bs.CssTooltipToggle className='flex-vbox justify-content-start py-1 w-100'>
                  <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                    <FeatherIcon.Search className="me-2 text-info" size={14} />
                    <span className='' style={{ fontSize: '0.9rem' }}>Partner No/ Tax Code</span>
                  </div>
                  <input.BBStringField className='px-2'
                    bean={{ bfsonePartnerCode: this.partnerPattern }} field='bfsonePartnerCode'
                    onInputChange={this.onChangeBFSOnePartnerCode} placeholder='Enter BFSOne Partner Code like CS011194 or Tax Code like *********' />
                </bs.CssTooltipToggle>

                <bs.CssTooltipContent className="d-flex flex-column rounded">
                  <div className="tooltip-header mb-2">
                    <span className="tooltip-title">Hướng dẫn nhập thông tin</span>
                  </div>
                  <div className="tooltip-body text-secondary">
                    Nếu bạn đã được phân quyền vào Customer có sẵn, hãy tìm kiếm thông tin bằng <b>Customer Code</b> hoặc <b>TaxCode</b> tương ứng để cập nhật.
                  </div>
                </bs.CssTooltipContent>

              </bs.CssTooltip>
            </bs.Col>
          </bs.Row>

          {
            partnerType === 'Customer'
              ? <>
                <bs.Row>
                  <bs.Col span={6}>
                    <input.BBStringField
                      bean={partner} field='bfsonePartnerCode' label={T("Partner No.")} disable />
                  </bs.Col>
                  <bs.Col span={6}>
                    <input.BBSelectField
                      bean={partner} field='category' label={T("Category.")} disable={!writeCap || !observer.isNewBean()}
                      options={this.categories} />
                  </bs.Col>
                </bs.Row>

                <bs.Row>
                  <bs.Col span={6}>
                    <input.BBStringField
                      bean={partner} field='name' label={T("Partner Name (Abb)")} disable={!writeCap} required
                      onInputChange={this.onUpdateSimilarFields} />
                  </bs.Col>
                  <bs.Col span={6}>
                    <input.BBStringField
                      bean={partner} field='taxCode' label={T("Tax Code")} disable={!writeCap} required
                      onBgInputChange={this.onCheckTaxCode} />
                  </bs.Col>
                </bs.Row>

              </>
              :
              <bs.Row>
                <bs.Col span={3}>
                  <input.BBStringField
                    bean={partner} field='bfsonePartnerCode' label={T("Partner No.")} disable />
                </bs.Col>
                <bs.Col span={3}>
                  <input.BBSelectField
                    bean={partner} field='category' label={T("Category.")} disable={!writeCap || !observer.isNewBean()}
                    options={this.categories} />
                </bs.Col>
                <bs.Col span={6}>
                  <input.BBStringField
                    bean={partner} field='name' label={T("Partner Name (Abb)")} required disable={!writeCap}
                    onInputChange={this.onUpdateSimilarFields} />
                </bs.Col>
              </bs.Row>
          }

          {
            partnerType === 'Agent' ?
              <bs.Row>
                <bs.Col span={3}>
                  <BBRefCountry key={partner.kcnCode}
                    appContext={appContext} pageContext={pageContext}
                    placement="bottom-start" offset={[0, 5]} minWidth={350}
                    disable={!writeCap} label={T('Country')} placeholder="Enter Country"
                    required bean={partner} beanIdField={'countryId'} hideMoreInfo
                    beanLabelField={'countryLabel'} refCountryBy='id'
                    onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                      bean['countryId'] = selectOpt['id'];
                      bean['countryLabel'] = selectOpt['label'];
                      this.forceUpdate();
                    }}
                  />
                </bs.Col>
                <bs.Col span={3}>
                  <input.BBStringField bean={partner} label={T('Investment Origin')} field="investmentOrigin" disable={!writeCap} />
                </bs.Col>

                <bs.Col span={3}>
                  <input.BBStringField
                    bean={partner} field='label' label={T("Partner Name (En)")} disable={!writeCap} />
                </bs.Col>

                <bs.Col span={3}>
                  <input.BBStringField
                    bean={partner} field='localizedLabel' label={T("Partner Name (VN)")} disable={!writeCap} />
                </bs.Col>
              </bs.Row>
              :
              <>
                <bs.Row>
                  <bs.Col span={6}>
                    <input.BBStringField
                      bean={partner} field='label' label={T("Partner Name (En)")} disable={!writeCap} />
                  </bs.Col>

                  <bs.Col span={6}>
                    <input.BBStringField
                      bean={partner} field='localizedLabel' label={T("Partner Name (VN)")} disable={!writeCap} />
                  </bs.Col>
                </bs.Row>
                <bs.Row>
                  <bs.Col span={3}>
                    <BBRefCountry key={util.IDTracker.next()}
                      appContext={appContext} pageContext={pageContext}
                      placement="bottom-start" offset={[0, 5]} minWidth={350}
                      disable={!writeCap} label={T('Country')} placeholder="Enter Country"
                      required bean={partner} beanIdField={'countryId'} hideMoreInfo
                      beanLabelField={'countryLabel'} refCountryBy='id'
                      onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                        bean['countryId'] = selectOpt['id'];
                        bean['countryLabel'] = selectOpt['label'];
                        this.forceUpdate();
                      }}
                    />
                  </bs.Col>

                  <bs.Col span={3}>
                    <BBRefState key={util.IDTracker.next()}
                      appContext={appContext} pageContext={pageContext}
                      placement="bottom-start" offset={[0, 5]} minWidth={350}
                      disable={!writeCap} label={T('Province')} placeholder="Enter Province"
                      required bean={partner} beanIdField={'provinceId'} hideMoreInfo
                      beanLabelField={'provinceLabel'} countryId={partner.countryId}
                    />
                  </bs.Col>
                  <bs.Col span={3}>
                    <BBRefLocation label='KCN'
                      appContext={appContext} pageContext={pageContext} bean={partner}
                      beanIdField={'kcnCode'} beanLabelField={'kcnLabel'} placeholder='KCN' hideMoreInfo
                      disable={!writeCap} inputObserver={observer} refLocationBy='id' locationTypes={['KCN']}
                      beanRefLabelField='label'
                      onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                        if (selectOpt && selectOpt['id']) {
                          bean['countryId'] = selectOpt['countryId'];
                          bean['countryLabel'] = selectOpt['countryLabel'];
                          bean['provinceId'] = selectOpt['stateId'];
                          bean['provinceLabel'] = selectOpt['stateLabel'];
                          bean['kcnLabel'] = selectOpt['label'];
                          bean['address'] = selectOpt['address'];
                          this.forceUpdate();
                        }
                      }} />
                  </bs.Col>
                  <bs.Col span={3}>
                    <input.BBStringField bean={partner} label={T('Investment Origin')} field="investmentOrigin" disable={!writeCap} />
                  </bs.Col>
                </bs.Row>
              </>
          }

          <bs.Row>
            <bs.Col span={6}>
              <input.BBTextField
                bean={partner} label={T('Address (En)')} field="address" disable={!writeCap} required
                onInputChange={this.onUpdateSimilarFields} style={{ height: '4em' }} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBTextField
                bean={partner} label={T('Address (Vn)')} field="localizedAddress" disable={!writeCap} required
                style={{ height: '4em' }} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='personalContact' label={T("Personal Contact")} disable={!writeCap} required />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='cell' label={T("Cell Phone")} disable={!writeCap} required />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='fax' label={T("FAX.")} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='email' label={T("Email")} disable={!writeCap} required validators={[util.validator.EMAIL_VALIDATOR]} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBSelectField bean={partner} field="source" label={T('Source')}
                options={SOURCE_OPTIONS} disable={!writeCap} />
            </bs.Col>

            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='swiftCode' label={T("Swift Code.")} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='bankName' label={T("Bank Name.")} disable={!writeCap} />
            </bs.Col>

            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='bankAddress' label={T("Bank Address.")} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <module.resource.BBRefResource
                appContext={appContext} pageContext={pageContext}
                placement="bottom-start" offset={[0, 5]} minWidth={350}
                disable={!writeCap} label={T('Industry')} placeholder="Enter Industry"
                required bean={partner} beanIdField={'industryCode'} hideMoreInfo
                beanLabelField={'industryLabel'} resourceType={"industry"} refResourceBy="identifier" />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='routing' label={T("Route")} disable={!writeCap} placeholder='Enter Route'
              />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBSelectField
                bean={partner} field='scope' label={T("Location")} disable={!writeCap || !observer.isNewBean()}
                options={['Domestic', 'Overseas']}
              />
            </bs.Col>
          </bs.Row>

          <input.BBTextField
            bean={partner} label={T('Note')} field="note" disable={!writeCap}
            style={{ height: '8em', fontSize: '1rem' }} />

        </div>

        <bs.Toolbar className='border'>

          <entity.ButtonEntityCommit btnLabel={`${this.state.isSending ? 'Request Partner ...' : 'Request Partner'}`}
            appContext={appContext} pageContext={pageContext}
            observer={observer} hide={!writeCap} disable={this.state.isSending}
            commit={{
              entityLabel: T(title), context: 'company',
              service: "BFSOneCRMService", commitMethod: "createBFSOnePartner"
            }}
            onPreCommit={this.onPreCommit} onPostCommit={this.onPostCommit} />

        </bs.Toolbar>
      </div>
    )
  }
}
