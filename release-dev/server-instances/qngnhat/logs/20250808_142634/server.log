2025-08-08T14:26:35.659+07:00  INFO 83087 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 83087 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-08T14:26:35.661+07:00  INFO 83087 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-08T14:26:36.933+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.006+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 69 ms. Found 22 JPA repository interfaces.
2025-08-08T14:26:37.017+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.019+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-08T14:26:37.019+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.026+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 9 JPA repository interfaces.
2025-08-08T14:26:37.027+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.079+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 51 ms. Found 3 JPA repository interfaces.
2025-08-08T14:26:37.079+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.084+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-08-08T14:26:37.096+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.104+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 1 JPA repository interface.
2025-08-08T14:26:37.116+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.121+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-08-08T14:26:37.125+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.128+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-08T14:26:37.128+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.129+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-08T14:26:37.136+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.143+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-08-08T14:26:37.149+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.152+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-08T14:26:37.153+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.156+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-08T14:26:37.158+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.166+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-08-08T14:26:37.166+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.170+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-08-08T14:26:37.171+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.171+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-08T14:26:37.171+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.172+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-08T14:26:37.172+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.181+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 7 JPA repository interfaces.
2025-08-08T14:26:37.181+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.185+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 2 JPA repository interfaces.
2025-08-08T14:26:37.185+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.185+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-08T14:26:37.185+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.197+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-08-08T14:26:37.209+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.217+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 8 JPA repository interfaces.
2025-08-08T14:26:37.217+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.220+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-08-08T14:26:37.220+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.227+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 7 JPA repository interfaces.
2025-08-08T14:26:37.227+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.233+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-08-08T14:26:37.234+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.238+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-08-08T14:26:37.238+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.245+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 5 JPA repository interfaces.
2025-08-08T14:26:37.246+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.253+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 7 JPA repository interfaces.
2025-08-08T14:26:37.253+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.264+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 14 JPA repository interfaces.
2025-08-08T14:26:37.264+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.289+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 24 ms. Found 20 JPA repository interfaces.
2025-08-08T14:26:37.290+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.291+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-08T14:26:37.296+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.297+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-08T14:26:37.297+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.305+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-08-08T14:26:37.308+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.354+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 46 ms. Found 67 JPA repository interfaces.
2025-08-08T14:26:37.355+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.367+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 1 JPA repository interface.
2025-08-08T14:26:37.379+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-08T14:26:37.384+07:00  INFO 83087 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 4 JPA repository interfaces.
2025-08-08T14:26:37.652+07:00  INFO 83087 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-08T14:26:37.656+07:00  INFO 83087 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-08T14:26:37.934+07:00  WARN 83087 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-08T14:26:38.152+07:00  INFO 83087 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-08T14:26:38.155+07:00  INFO 83087 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-08T14:26:38.169+07:00  INFO 83087 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-08T14:26:38.169+07:00  INFO 83087 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2370 ms
2025-08-08T14:26:38.268+07:00  WARN 83087 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-08T14:26:38.269+07:00  INFO 83087 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-08T14:26:38.417+07:00  INFO 83087 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@51a42767
2025-08-08T14:26:38.417+07:00  INFO 83087 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-08T14:26:38.422+07:00  WARN 83087 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-08T14:26:38.422+07:00  INFO 83087 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-08T14:26:38.433+07:00  INFO 83087 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@5f92ef40
2025-08-08T14:26:38.433+07:00  INFO 83087 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-08T14:26:38.434+07:00  WARN 83087 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-08T14:26:38.434+07:00  INFO 83087 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-08T14:26:38.969+07:00  INFO 83087 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7c1f435a
2025-08-08T14:26:38.969+07:00  INFO 83087 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-08T14:26:38.969+07:00  WARN 83087 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-08T14:26:38.969+07:00  INFO 83087 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-08T14:26:38.979+07:00  INFO 83087 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@49d4a2cf
2025-08-08T14:26:38.979+07:00  INFO 83087 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-08T14:26:38.979+07:00  WARN 83087 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-08T14:26:38.979+07:00  INFO 83087 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-08T14:26:38.987+07:00  INFO 83087 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@401b7109
2025-08-08T14:26:38.987+07:00  INFO 83087 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-08T14:26:38.987+07:00  INFO 83087 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-08T14:26:39.037+07:00  INFO 83087 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-08T14:26:39.039+07:00  INFO 83087 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@383c94ed{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17798740780796959950/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@12f85dc8{STARTED}}
2025-08-08T14:26:39.039+07:00  INFO 83087 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@383c94ed{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17798740780796959950/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@12f85dc8{STARTED}}
2025-08-08T14:26:39.042+07:00  INFO 83087 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@417f7d7f{STARTING}[12.0.15,sto=0] @4378ms
2025-08-08T14:26:39.142+07:00  INFO 83087 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-08T14:26:39.179+07:00  INFO 83087 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-08T14:26:39.198+07:00  INFO 83087 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-08T14:26:39.341+07:00  INFO 83087 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-08T14:26:39.363+07:00  WARN 83087 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-08T14:26:39.989+07:00  INFO 83087 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-08T14:26:40.000+07:00  INFO 83087 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@34f4d22c] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-08T14:26:40.128+07:00  INFO 83087 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-08T14:26:40.302+07:00  INFO 83087 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-08-08T14:26:40.304+07:00  INFO 83087 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-08T14:26:40.310+07:00  INFO 83087 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-08T14:26:40.312+07:00  INFO 83087 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-08T14:26:40.336+07:00  INFO 83087 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-08T14:26:40.341+07:00  WARN 83087 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-08T14:26:42.518+07:00  INFO 83087 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-08T14:26:42.519+07:00  INFO 83087 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7f08d920] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-08T14:26:42.693+07:00  WARN 83087 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-08T14:26:42.693+07:00  WARN 83087 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-08T14:26:42.701+07:00  WARN 83087 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-08T14:26:42.701+07:00  WARN 83087 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-08T14:26:42.717+07:00  WARN 83087 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-08T14:26:42.717+07:00  WARN 83087 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-08T14:26:43.093+07:00  INFO 83087 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-08T14:26:43.098+07:00  INFO 83087 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-08T14:26:43.100+07:00  INFO 83087 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-08T14:26:43.114+07:00  INFO 83087 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-08T14:26:43.116+07:00  WARN 83087 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-08T14:26:43.629+07:00  INFO 83087 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-08T14:26:43.630+07:00  INFO 83087 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1fe7c233] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-08T14:26:43.690+07:00  WARN 83087 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-08T14:26:43.690+07:00  WARN 83087 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-08T14:26:44.048+07:00  INFO 83087 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-08T14:26:44.078+07:00  INFO 83087 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-08T14:26:44.083+07:00  INFO 83087 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-08T14:26:44.083+07:00  INFO 83087 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-08T14:26:44.089+07:00  WARN 83087 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-08T14:26:44.217+07:00  INFO 83087 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-08T14:26:44.678+07:00  INFO 83087 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-08T14:26:44.682+07:00  INFO 83087 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-08T14:26:44.717+07:00  INFO 83087 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-08T14:26:44.768+07:00  INFO 83087 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-08T14:26:44.825+07:00  INFO 83087 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-08T14:26:44.853+07:00  INFO 83087 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-08T14:26:44.876+07:00  INFO 83087 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 273091139ms : this is harmless.
2025-08-08T14:26:44.886+07:00  INFO 83087 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-08T14:26:44.889+07:00  INFO 83087 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-08T14:26:44.903+07:00  INFO 83087 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1210445854ms : this is harmless.
2025-08-08T14:26:44.905+07:00  INFO 83087 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-08T14:26:44.919+07:00  INFO 83087 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-08T14:26:44.920+07:00  INFO 83087 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-08T14:26:47.268+07:00  INFO 83087 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-08T14:26:47.269+07:00  INFO 83087 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-08T14:26:47.270+07:00  WARN 83087 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-08T14:26:47.397+07:00  INFO 83087 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/08/2025@14:15:00+0700 to 08/08/2025@14:30:00+0700
2025-08-08T14:26:47.398+07:00  INFO 83087 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/08/2025@14:15:00+0700 to 08/08/2025@14:30:00+0700
2025-08-08T14:26:47.864+07:00  INFO 83087 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-08T14:26:47.864+07:00  INFO 83087 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-08T14:26:47.865+07:00  WARN 83087 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-08T14:26:48.086+07:00  INFO 83087 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-08T14:26:48.086+07:00  INFO 83087 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-08T14:26:48.086+07:00  INFO 83087 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-08T14:26:48.086+07:00  INFO 83087 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-08T14:26:48.086+07:00  INFO 83087 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-08T14:26:49.806+07:00  WARN 83087 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 4bf4de3b-bdad-4fb5-a2b6-fe8cb496023b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-08T14:26:49.810+07:00  INFO 83087 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-08T14:26:50.132+07:00  INFO 83087 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-08T14:26:50.136+07:00  INFO 83087 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-08T14:26:50.136+07:00  INFO 83087 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-08T14:26:50.136+07:00  INFO 83087 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-08T14:26:50.188+07:00  INFO 83087 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-08T14:26:50.188+07:00  INFO 83087 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-08T14:26:50.191+07:00  INFO 83087 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-08T14:26:50.199+07:00  INFO 83087 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@3786c87a{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-08T14:26:50.200+07:00  INFO 83087 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-08T14:26:50.201+07:00  INFO 83087 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-08T14:26:50.229+07:00  INFO 83087 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-08T14:26:50.230+07:00  INFO 83087 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-08T14:26:50.235+07:00  INFO 83087 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 15.308 seconds (process running for 15.572)
2025-08-08T14:27:04.248+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:27:04.495+07:00  INFO 83087 --- [qtp173576304-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01sb7zhxp2k0i41ksg4xod3d6pc0
2025-08-08T14:27:04.810+07:00  INFO 83087 --- [qtp173576304-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01sb7zhxp2k0i41ksg4xod3d6pc0, token = 3931c172be567360dab50adf5214db31
2025-08-08T14:27:05.316+07:00  INFO 83087 --- [qtp173576304-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-08T14:27:28.807+07:00  INFO 83087 --- [qtp173576304-64] n.d.m.data.input.DataMappingService      : Cell Style counting, 2 
2025-08-08T14:27:36.822+07:00 ERROR 83087 --- [qtp173576304-61] n.d.m.monitor.call.EndpointCallContext   : Cannot find the component = class net.datatp.module.dtable.DTableService, mehthod = exportDTableTemplateAsXlsx, userParams = 
 {
  "dtable" : {
    "id" : 106,
    "uikey" : null,
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 21,
    "createdBy" : "nhat.le",
    "createdTime" : "06/11/2024@09:30:13+0000",
    "modifiedBy" : "nhat.le",
    "modifiedTime" : "07/08/2025@07:50:36+0000",
    "storageState" : "ACTIVE",
    "companyId" : 4,
    "code" : "agency-metting-conference-report",
    "label" : "AGENCY MEETING - CONFERENCE REPORT",
    "ownerAccountId" : 2089,
    "ownerLabel" : "LE QUANG NHAT",
    "description" : null,
    "summarySupport" : false,
    "summaryCellHeight" : null,
    "minRowAccessCapability" : "None",
    "type" : "GENERIC",
    "jsPlugins" : [ "" ],
    "columnGroups" : [ {
      "id" : 486,
      "uikey" : null,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "companyId" : 4,
      "idx" : 0,
      "name" : "group-0",
      "label" : null,
      "visible" : true,
      "columns" : [ {
        "id" : 2405,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 0,
        "columnId" : "date",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "Date",
        "width" : 150,
        "description" : null,
        "type" : "Date",
        "format" : "None",
        "options" : null,
        "groupBy" : true,
        "sortable" : true,
        "sortPriority" : 1,
        "sortDirection" : "ASC",
        "filterable" : true,
        "searchable" : false,
        "container" : "default",
        "showState" : true,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2406,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 1,
        "columnId" : "time",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "Time",
        "width" : 90,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2407,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 2,
        "columnId" : "agent-namecountryweb",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "Agent Name/Country/Web",
        "width" : 250,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2408,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 3,
        "columnId" : "pic-contactemail",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "PIC CONTACT/Email",
        "width" : 250,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2409,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 4,
        "columnId" : "network",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "NETWORK",
        "width" : 200,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2410,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 5,
        "columnId" : "meeting-content",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "MEETING CONTENT",
        "width" : 450,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2411,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 6,
        "columnId" : "advantage-services",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "ADVANTAGE SERVICES",
        "width" : 450,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2412,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 7,
        "columnId" : "target-market",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "TARGET MARKET",
        "width" : 300,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2413,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 8,
        "columnId" : "follow-up",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "FOLLOW UP",
        "width" : 200,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2414,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 9,
        "columnId" : "potential-ranking-1-2-3",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "Potential Ranking (1-2-3)",
        "width" : 200,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : [ "1", "2", "3" ],
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2415,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 10,
        "columnId" : "remark",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "REMARK",
        "width" : 200,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      } ],
      "minColumnAccessCapability" : "None"
    } ],
    "jsMacros" : [ {
      "id" : 172,
      "uikey" : null,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "companyId" : 4,
      "name" : "init",
      "label" : "init",
      "description" : null,
      "macro" : "let listener = {\n  onInitConfig: (context, config) => {\n    let fieldsConfig = config.record.fields;\n    for (let field of fieldsConfig) {\n      let name = field.name;\n      if (name === 'ownerLabel') {\n        field.filterable = true;\n        field.filterableType = 'options';\n      }\n      if ([\n        'agent-namecountryweb', 'pic-contactemail', 'network', 'meeting-content',\n        'advantage-services', 'target-market', 'follow-up', 'remark', 'time'\n      ].includes(name) && field.editor) field.editor.type = 'Text';\n    }\n\n    config.record.computeDataRowHeight = (dRec) => {\n      let record = dRec.record;\n      if (record && record['_rowHeight_']) {\n        return record['_rowHeight_'];\n      }\n      return 134;\n    }\n  },\n}\nSCONTEXT.gridContext.registerListener('init', listener);"
    } ],
    "plugins" : [ ],
    "permissions" : [ {
      "id" : 1059,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 2089,
      "label" : "Le Quang Nhat",
      "capability" : "Admin",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 106
    }, {
      "id" : 1060,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 11174,
      "label" : "Đinh Thanh Minh",
      "capability" : "Moderator",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 106
    }, {
      "id" : 1061,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 11702,
      "label" : "CHÂU SAO MAI",
      "capability" : "Admin",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 106
    }, {
      "id" : 1063,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 2084,
      "label" : "Nguyen Tuan Anh",
      "capability" : "Admin",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 106
    }, {
      "id" : 1070,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 14573,
      "label" : "NGUYỄN THỊ PHƯƠNG THẢO",
      "capability" : "Write",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 106
    }, {
      "id" : 1071,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 14574,
      "label" : "NGUYỄN THANH HẢI",
      "capability" : "Write",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 106
    }, {
      "id" : 1083,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 14572,
      "label" : "TRÀ THỊ NHƯ QUỲNH",
      "capability" : "Write",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 106
    }, {
      "id" : 1085,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 3,
      "label" : "Lê Ngọc Đàn",
      "capability" : "Admin",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 106
    } ]
  }
}
2025-08-08T14:27:36.840+07:00 ERROR 83087 --- [qtp173576304-61] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint DTableService/exportDTableTemplateAsXlsx
2025-08-08T14:27:36.840+07:00 ERROR 83087 --- [qtp173576304-61] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: Cannot find the component class net.datatp.module.dtable.DTableService, mehtod exportDTableTemplateAsXlsx
	at net.datatp.util.error.RuntimeError.IllegalArgument(RuntimeError.java:61)
	at net.datatp.module.monitor.call.EndpointCallContext.computeArguments(EndpointCallContext.java:96)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:150)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-08T14:27:53.447+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-08-08T14:27:53.506+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:28:06.534+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:29:03.628+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:29:52.751+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-08T14:29:52.756+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:30:06.782+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:30:06.788+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-08T14:30:06.800+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-08T14:30:06.804+07:00  INFO 83087 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 08/08/2025@14:30:06+0700
2025-08-08T14:30:06.817+07:00  INFO 83087 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/08/2025@14:30:00+0700 to 08/08/2025@14:45:00+0700
2025-08-08T14:30:06.817+07:00  INFO 83087 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/08/2025@14:30:00+0700 to 08/08/2025@14:45:00+0700
2025-08-08T14:31:02.941+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:31:57.116+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-08T14:31:57.133+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:32:06.155+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:33:02.245+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:33:56.351+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:33:56.382+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:34:05.398+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:35:06.500+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:35:06.511+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-08T14:35:56.608+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-08T14:35:56.625+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:36:04.645+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:36:06.873+07:00  INFO 83087 --- [qtp173576304-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01sb7zhxp2k0i41ksg4xod3d6pc0, token = 3931c172be567360dab50adf5214db31
2025-08-08T14:36:06.881+07:00  INFO 83087 --- [qtp173576304-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01sb7zhxp2k0i41ksg4xod3d6pc0, token = 3931c172be567360dab50adf5214db31
2025-08-08T14:36:06.895+07:00  INFO 83087 --- [qtp173576304-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-08T14:36:06.897+07:00  INFO 83087 --- [qtp173576304-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-08T14:36:24.978+07:00  INFO 83087 --- [qtp173576304-65] n.d.m.data.input.DataMappingService      : Cell Style counting, 1 
2025-08-08T14:36:41.098+07:00 ERROR 83087 --- [qtp173576304-36] n.d.m.monitor.call.EndpointCallContext   : Cannot find the component = class net.datatp.module.dtable.DTableService, mehthod = exportDTableTemplateAsXlsx, userParams = 
 {
  "dtable" : {
    "id" : 107,
    "uikey" : null,
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 26,
    "createdBy" : "nhat.le",
    "createdTime" : "09/06/2025@13:56:39+0000",
    "modifiedBy" : "nhat.le",
    "modifiedTime" : "07/08/2025@08:42:59+0000",
    "storageState" : "ACTIVE",
    "companyId" : 4,
    "code" : "ist-daily-report",
    "label" : "QUOTATION DAILY REPORT - IST",
    "ownerAccountId" : 2089,
    "ownerLabel" : "LE QUANG NHAT",
    "description" : null,
    "summarySupport" : null,
    "summaryCellHeight" : null,
    "minRowAccessCapability" : "None",
    "type" : "GENERIC",
    "jsPlugins" : [ "bd-ist-daily-report-vgrid-plugin" ],
    "columnGroups" : [ {
      "id" : 487,
      "uikey" : null,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "companyId" : 4,
      "idx" : 0,
      "name" : "group-0",
      "label" : null,
      "visible" : true,
      "columns" : [ {
        "id" : 2424,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 0,
        "columnId" : "agent-name",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "AGENT NAME",
        "width" : 230,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : true,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : true,
        "searchable" : false,
        "container" : "default",
        "showState" : true,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2425,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 1,
        "columnId" : "country",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "COUNTRY",
        "width" : 170,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : true,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : true,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2426,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 2,
        "columnId" : "network",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "NETWORK",
        "width" : 100,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : true,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : true,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2427,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 3,
        "columnId" : "source",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "SOURCE",
        "width" : 120,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : [ "New", "Existed" ],
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : true,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      } ],
      "minColumnAccessCapability" : "None"
    }, {
      "id" : 488,
      "uikey" : null,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "companyId" : 4,
      "idx" : 1,
      "name" : "group-1",
      "label" : "EXPORT INQUIRIES",
      "visible" : true,
      "columns" : [ {
        "id" : 2428,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 4,
        "columnId" : "exp-20",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "20' E",
        "width" : 100,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2429,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 5,
        "columnId" : "exp-40",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "40' E",
        "width" : 100,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2448,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 6,
        "columnId" : "exp-45",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "45' E",
        "width" : 100,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2430,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 7,
        "columnId" : "exp-lcl-cbm",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "LCL (CBM) E",
        "width" : 100,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2431,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 8,
        "columnId" : "exp-air-kgs",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "AIR (KGS) E",
        "width" : 100,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2432,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 9,
        "columnId" : "exp-breakbulk-shmt",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "BREAKBULK (SHPT) E",
        "width" : 170,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      } ],
      "minColumnAccessCapability" : "None"
    }, {
      "id" : 489,
      "uikey" : null,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "companyId" : 4,
      "idx" : 2,
      "name" : "group-2",
      "label" : "IMPORT INQUIRIES",
      "visible" : true,
      "columns" : [ {
        "id" : 2433,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 10,
        "columnId" : "imp-20",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "20' I",
        "width" : 100,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2434,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 11,
        "columnId" : "imp-40",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "40' I",
        "width" : 100,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2449,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 12,
        "columnId" : "imp-45",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "45' I",
        "width" : 100,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2435,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 13,
        "columnId" : "imp-lcl-cbm",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "LCL (CBM) I",
        "width" : 100,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2436,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 14,
        "columnId" : "imp-air-kgs",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "AIR (KGS) I",
        "width" : 100,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2437,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 15,
        "columnId" : "breakbulk-shpt-i",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "BREAKBULK (SHPT) I",
        "width" : 170,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      } ],
      "minColumnAccessCapability" : "None"
    }, {
      "id" : 490,
      "uikey" : null,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "companyId" : 4,
      "idx" : 3,
      "name" : "group-3",
      "label" : null,
      "visible" : true,
      "columns" : [ {
        "id" : 2438,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 16,
        "columnId" : "incoterms",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "INCOTERMS",
        "width" : 150,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2439,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 17,
        "columnId" : "route",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "ROUTE",
        "width" : 120,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2440,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 18,
        "columnId" : "date",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "DATE",
        "width" : 140,
        "description" : null,
        "type" : "Date",
        "format" : "Date",
        "options" : null,
        "groupBy" : false,
        "sortable" : true,
        "sortPriority" : 1,
        "sortDirection" : "DESC",
        "filterable" : true,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2441,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 19,
        "columnId" : "subject-mail",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "SUBJECT MAIL",
        "width" : 250,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2442,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 20,
        "columnId" : "pic",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "PIC",
        "width" : 200,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2443,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 21,
        "columnId" : "email",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "EMAIL",
        "width" : 300,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2444,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 22,
        "columnId" : "reasonnote",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "NOTE",
        "width" : 300,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "default",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2445,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 23,
        "columnId" : "status",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "STATUS",
        "width" : 130,
        "description" : null,
        "type" : "String",
        "format" : "None",
        "options" : [ "Checking", "Quoted", "Followed Up", "Won", "Lost" ],
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : true,
        "searchable" : false,
        "container" : "fixed-right",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2446,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 24,
        "columnId" : "quotation-qty",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "QUOTATION Q'TY",
        "width" : 130,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "fixed-right",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      }, {
        "id" : 2451,
        "uikey" : null,
        "editState" : "ORIGIN",
        "loadState" : "FromDB",
        "companyId" : 4,
        "idx" : 25,
        "columnId" : "booking-qty",
        "refColumnId" : null,
        "refDTableCode" : null,
        "label" : "BOOKING Q'TY",
        "width" : 130,
        "description" : null,
        "type" : "Double",
        "format" : "None",
        "options" : null,
        "groupBy" : false,
        "sortable" : false,
        "sortPriority" : null,
        "sortDirection" : "ASC",
        "filterable" : false,
        "searchable" : false,
        "container" : "fixed-right",
        "showState" : false,
        "autoCompleteType" : "None",
        "allowUserInput" : false,
        "summaryCellMode" : "None"
      } ],
      "minColumnAccessCapability" : "None"
    } ],
    "jsMacros" : [ {
      "id" : 173,
      "uikey" : null,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "companyId" : 4,
      "name" : "init",
      "label" : "init",
      "description" : null,
      "macro" : "let listener = {\n  onInitConfig: (context, config) => {\n    config.record.computeDataRowHeight = (dRec) => {\n      return 70;\n    }\n    let fieldsConfig = config.record.fields;\n\n    for (let field of fieldsConfig) {\n      if (field.name === 'agent-name') {\n        field.filterable = true;\n        field.filterableType = 'options';\n      }\n      if (field.name === 'country' || field.name === 'source') {\n        field.filterable = true;\n        field.filterableType = 'options';\n      }\n\n      if (field.name === 'rate-success-win') {\n        field.computeCssClasses = (ctx, dRecord) => {\n          return 'justify-content-end';\n        }\n\n        field.customRender = (ctx, field, dRec, focus) => {\n          let record = dRec.record;\n          return record['rate-success-win'] ? record['rate-success-win'].toFixed(2) + \"%\" : '0%'\n        }\n\n        field.editor.DO_NOT_USE_renderCustom = (field, dRec, tabIndex, focus, onInputChange) => {\n          let record = dRec.record;\n          return record['rate-success-win'] ? record['rate-success-win'].toFixed(2) + \"%\" : '0%'\n        }\n      }\n\n      if (field.name === 'pic' || field.name === 'email' || field.name === 'agent-name' || field.name === 'incoterms' || field.name === 'reasonnote' || field.name === 'subject-mail') {\n        if (field.editor) field.editor.type = 'Text';\n      }\n\n      if ((field.name === 'booking-qty' || field.name === 'quotation-qty') && field.editor) {\n        field.editor.DO_NOT_USE_onInputChange = (ctx, dRec, fieldInput, oldVal, newVal) => {\n          let record = dRec.record;\n          if (record['booking-qty'] !== 0 && record['quotation-qty'] !== 0) record['rate-success-win'] = record['booking-qty'] * 100 / record['quotation-qty'];\n          else record['rate-success-win'] = 0.0;\n          let event = {\n            row: dRec.row, field: field, event: 'Modified'\n          };\n          context.broadcastCellEvent(event);\n          context.vgrid?.forceUpdateView();\n        };\n      }\n    }\n  },\n}\nSCONTEXT.gridContext.registerListener('init', listener);"
    } ],
    "plugins" : [ ],
    "permissions" : [ {
      "id" : 1077,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 14574,
      "label" : "NGUYỄN THANH HẢI",
      "capability" : "Moderator",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 107
    }, {
      "id" : 1081,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 2084,
      "label" : "Nguyen Tuan Anh",
      "capability" : "Admin",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 107
    }, {
      "id" : 1086,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 3,
      "label" : "Lê Ngọc Đàn",
      "capability" : "Admin",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 107
    }, {
      "id" : 1087,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 57380,
      "label" : "TRẦN THỊ HOA",
      "capability" : "Write",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 107
    }, {
      "id" : 1080,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 2089,
      "label" : "Le Quang Nhat",
      "capability" : "Write",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 107
    }, {
      "id" : 1088,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 11699,
      "label" : "NGÔ NHẬT LINH",
      "capability" : "Write",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 107
    }, {
      "id" : 1082,
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "userId" : 55765,
      "label" : "PHẠM THỊ KIM TUYỀN",
      "capability" : "Write",
      "type" : "Employee",
      "companyId" : 4,
      "dtableId" : 107
    } ]
  }
}
2025-08-08T14:36:41.114+07:00 ERROR 83087 --- [qtp173576304-36] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint DTableService/exportDTableTemplateAsXlsx
2025-08-08T14:36:41.114+07:00 ERROR 83087 --- [qtp173576304-36] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: Cannot find the component class net.datatp.module.dtable.DTableService, mehtod exportDTableTemplateAsXlsx
	at net.datatp.util.error.RuntimeError.IllegalArgument(RuntimeError.java:61)
	at net.datatp.module.monitor.call.EndpointCallContext.computeArguments(EndpointCallContext.java:96)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:150)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-08T14:37:06.766+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:37:56.911+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 1
2025-08-08T14:37:56.927+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:38:03.951+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:38:37.318+07:00  INFO 83087 --- [qtp173576304-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01sb7zhxp2k0i41ksg4xod3d6pc0, token = 3931c172be567360dab50adf5214db31
2025-08-08T14:38:37.320+07:00  INFO 83087 --- [qtp173576304-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01sb7zhxp2k0i41ksg4xod3d6pc0, token = 3931c172be567360dab50adf5214db31
2025-08-08T14:38:37.364+07:00  INFO 83087 --- [qtp173576304-72] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-08T14:38:37.364+07:00  INFO 83087 --- [qtp173576304-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-08T14:39:06.068+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:39:56.157+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:39:56.166+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:40:03.180+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:40:03.180+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-08T14:40:38.999+07:00  INFO 83087 --- [qtp173576304-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01sb7zhxp2k0i41ksg4xod3d6pc0, token = 3931c172be567360dab50adf5214db31
2025-08-08T14:40:39.001+07:00  INFO 83087 --- [qtp173576304-77] n.d.module.session.ClientSessionManager  : Add a client session id = node01sb7zhxp2k0i41ksg4xod3d6pc0, token = 3931c172be567360dab50adf5214db31
2025-08-08T14:40:39.016+07:00  INFO 83087 --- [qtp173576304-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-08T14:40:39.026+07:00  INFO 83087 --- [qtp173576304-72] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-08T14:41:04.957+07:00  INFO 83087 --- [qtp173576304-95] n.d.m.data.input.DataMappingService      : Cell Style counting, 2 
2025-08-08T14:41:06.961+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:41:56.091+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 1
2025-08-08T14:41:56.110+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:42:03.123+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:43:06.229+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:43:55.337+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:43:55.361+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:44:02.376+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:44:27.859+07:00  INFO 83087 --- [qtp173576304-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01sb7zhxp2k0i41ksg4xod3d6pc0, token = 3931c172be567360dab50adf5214db31
2025-08-08T14:44:27.867+07:00  INFO 83087 --- [qtp173576304-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01sb7zhxp2k0i41ksg4xod3d6pc0, token = 3931c172be567360dab50adf5214db31
2025-08-08T14:44:27.875+07:00  INFO 83087 --- [qtp173576304-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-08T14:44:27.875+07:00  INFO 83087 --- [qtp173576304-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-08T14:45:05.491+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:45:05.495+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-08T14:45:05.496+07:00  INFO 83087 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 08/08/2025@14:45:05+0700
2025-08-08T14:45:05.542+07:00  INFO 83087 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/08/2025@14:45:00+0700 to 08/08/2025@15:00:00+0700
2025-08-08T14:45:05.542+07:00  INFO 83087 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/08/2025@14:45:00+0700 to 08/08/2025@15:00:00+0700
2025-08-08T14:45:05.542+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-08T14:45:54.637+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-08-08T14:45:54.649+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:46:06.661+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:47:04.716+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:47:53.809+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-08T14:47:53.811+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:48:06.828+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:49:03.917+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:49:52.986+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:49:52.996+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:50:06.012+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-08T14:50:06.013+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:51:03.126+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:51:57.252+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-08T14:51:57.278+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:52:06.298+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:53:02.417+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:53:56.547+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-08T14:53:56.567+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:54:05.590+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:55:06.712+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:55:06.716+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-08T14:55:56.811+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 8
2025-08-08T14:55:56.823+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:56:04.839+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:56:39.075+07:00  INFO 83087 --- [Scheduler-1695099356-1] n.d.m.session.AppHttpSessionListener     : The session node01sb7zhxp2k0i41ksg4xod3d6pc0 is destroyed.
2025-08-08T14:57:06.938+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:57:57.022+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:57:57.032+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:58:04.044+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:59:06.136+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T14:59:56.226+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T14:59:56.234+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T15:00:03.247+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-08T15:00:03.250+07:00  INFO 83087 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 08/08/2025@15:00:03+0700
2025-08-08T15:00:03.286+07:00  INFO 83087 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 08/08/2025@15:00:00+0700 to 08/08/2025@15:15:00+0700
2025-08-08T15:00:03.286+07:00  INFO 83087 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 08/08/2025@15:00:00+0700 to 08/08/2025@15:15:00+0700
2025-08-08T15:00:03.302+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-08T15:00:03.303+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-08-08T15:00:03.303+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:00:03.304+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-08T15:01:06.394+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:01:55.494+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-08T15:01:55.519+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T15:02:02.533+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:03:05.645+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:03:54.724+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T15:03:54.747+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T15:04:06.764+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:05:04.858+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:05:04.862+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-08T15:05:53.927+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T15:05:53.934+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T15:06:06.959+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:07:04.061+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:07:53.146+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T15:07:53.152+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T15:08:06.176+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:09:03.283+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:09:52.362+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T15:09:52.369+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T15:10:06.384+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:10:06.385+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-08T15:11:02.465+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:11:56.558+07:00  INFO 83087 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-08T15:11:56.572+07:00  INFO 83087 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-08T15:12:05.592+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-08T15:13:06.688+07:00  INFO 83087 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
