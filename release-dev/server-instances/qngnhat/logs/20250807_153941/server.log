2025-08-07T15:39:41.837+07:00  INFO 90416 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 90416 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-07T15:39:41.838+07:00  INFO 90416 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-07T15:39:42.563+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.628+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 62 ms. Found 22 JPA repository interfaces.
2025-08-07T15:39:42.637+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.638+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-07T15:39:42.638+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.645+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-08-07T15:39:42.646+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.649+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-07T15:39:42.650+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.690+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 39 ms. Found 6 JPA repository interfaces.
2025-08-07T15:39:42.701+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.706+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-08-07T15:39:42.715+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.720+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-08-07T15:39:42.724+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.726+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-07T15:39:42.727+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.727+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-07T15:39:42.732+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.738+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-08-07T15:39:42.742+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.745+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-07T15:39:42.745+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.748+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-07T15:39:42.750+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.757+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-08-07T15:39:42.757+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.760+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-08-07T15:39:42.761+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.761+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-07T15:39:42.761+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.762+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-07T15:39:42.762+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.766+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-08-07T15:39:42.766+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.767+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-08-07T15:39:42.768+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.768+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-07T15:39:42.768+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.778+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-08-07T15:39:42.787+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.793+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-08-07T15:39:42.793+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.796+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-07T15:39:42.796+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.800+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-08-07T15:39:42.801+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.806+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-08-07T15:39:42.806+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.810+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-07T15:39:42.810+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.813+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-07T15:39:42.813+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.818+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-07T15:39:42.818+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.828+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-08-07T15:39:42.828+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.840+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 20 JPA repository interfaces.
2025-08-07T15:39:42.840+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.841+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-07T15:39:42.847+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.847+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-07T15:39:42.847+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.854+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-08-07T15:39:42.856+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.892+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 67 JPA repository interfaces.
2025-08-07T15:39:42.892+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.894+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-07T15:39:42.899+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T15:39:42.902+07:00  INFO 90416 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-08-07T15:39:43.084+07:00  INFO 90416 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-07T15:39:43.088+07:00  INFO 90416 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-07T15:39:43.352+07:00  WARN 90416 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-07T15:39:43.549+07:00  INFO 90416 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-07T15:39:43.552+07:00  INFO 90416 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-07T15:39:43.564+07:00  INFO 90416 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-07T15:39:43.564+07:00  INFO 90416 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1601 ms
2025-08-07T15:39:43.622+07:00  WARN 90416 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-07T15:39:43.622+07:00  INFO 90416 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-07T15:39:43.764+07:00  INFO 90416 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@16bed816
2025-08-07T15:39:43.764+07:00  INFO 90416 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-07T15:39:43.769+07:00  WARN 90416 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-07T15:39:43.769+07:00  INFO 90416 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-07T15:39:43.774+07:00  INFO 90416 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@508863eb
2025-08-07T15:39:43.774+07:00  INFO 90416 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-07T15:39:43.774+07:00  WARN 90416 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-07T15:39:43.774+07:00  INFO 90416 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-07T15:39:44.277+07:00  INFO 90416 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@431bf770
2025-08-07T15:39:44.277+07:00  INFO 90416 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-07T15:39:44.277+07:00  WARN 90416 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-07T15:39:44.277+07:00  INFO 90416 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-07T15:39:44.284+07:00  INFO 90416 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@10bf2726
2025-08-07T15:39:44.284+07:00  INFO 90416 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-07T15:39:44.284+07:00  WARN 90416 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-07T15:39:44.285+07:00  INFO 90416 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-07T15:39:44.292+07:00  INFO 90416 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@5135b459
2025-08-07T15:39:44.292+07:00  INFO 90416 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-07T15:39:44.292+07:00  INFO 90416 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-07T15:39:44.340+07:00  INFO 90416 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-07T15:39:44.342+07:00  INFO 90416 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@6f636de3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16479615897773935899/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@522fdf0c{STARTED}}
2025-08-07T15:39:44.343+07:00  INFO 90416 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@6f636de3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16479615897773935899/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@522fdf0c{STARTED}}
2025-08-07T15:39:44.344+07:00  INFO 90416 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7fec354{STARTING}[12.0.15,sto=0] @3041ms
2025-08-07T15:39:44.397+07:00  INFO 90416 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-07T15:39:44.429+07:00  INFO 90416 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-07T15:39:44.445+07:00  INFO 90416 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-07T15:39:44.568+07:00  INFO 90416 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-07T15:39:44.594+07:00  WARN 90416 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-07T15:39:45.224+07:00  INFO 90416 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-07T15:39:45.239+07:00  INFO 90416 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@703b55db] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-07T15:39:45.348+07:00  INFO 90416 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T15:39:45.527+07:00  INFO 90416 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-08-07T15:39:45.529+07:00  INFO 90416 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-07T15:39:45.534+07:00  INFO 90416 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-07T15:39:45.536+07:00  INFO 90416 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-07T15:39:45.562+07:00  INFO 90416 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-07T15:39:45.565+07:00  WARN 90416 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-07T15:39:47.699+07:00  INFO 90416 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-07T15:39:47.700+07:00  INFO 90416 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7b3b2c9a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-07T15:39:47.882+07:00  WARN 90416 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-07T15:39:47.882+07:00  WARN 90416 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-07T15:39:47.889+07:00  WARN 90416 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-07T15:39:47.889+07:00  WARN 90416 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-07T15:39:47.904+07:00  WARN 90416 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-07T15:39:47.904+07:00  WARN 90416 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-07T15:39:48.276+07:00  INFO 90416 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T15:39:48.282+07:00  INFO 90416 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-07T15:39:48.283+07:00  INFO 90416 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-07T15:39:48.297+07:00  INFO 90416 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-07T15:39:48.299+07:00  WARN 90416 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-07T15:39:48.817+07:00  INFO 90416 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-07T15:39:48.817+07:00  INFO 90416 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1e98dca] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-07T15:39:48.868+07:00  WARN 90416 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-07T15:39:48.868+07:00  WARN 90416 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-07T15:39:49.182+07:00  INFO 90416 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T15:39:49.212+07:00  INFO 90416 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-07T15:39:49.216+07:00  INFO 90416 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-07T15:39:49.217+07:00  INFO 90416 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-07T15:39:49.223+07:00  WARN 90416 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-07T15:39:49.356+07:00  INFO 90416 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-07T15:39:49.870+07:00  INFO 90416 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-07T15:39:49.874+07:00  INFO 90416 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-07T15:39:49.910+07:00  INFO 90416 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-07T15:39:49.953+07:00  INFO 90416 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-07T15:39:50.026+07:00  INFO 90416 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-07T15:39:50.060+07:00  INFO 90416 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-07T15:39:50.085+07:00  INFO 90416 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 191080389ms : this is harmless.
2025-08-07T15:39:50.093+07:00  INFO 90416 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-07T15:39:50.097+07:00  INFO 90416 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-07T15:39:50.110+07:00  INFO 90416 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1128435103ms : this is harmless.
2025-08-07T15:39:50.111+07:00  INFO 90416 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-07T15:39:50.131+07:00  INFO 90416 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-07T15:39:50.132+07:00  INFO 90416 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-07T15:39:52.806+07:00  INFO 90416 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-07T15:39:52.806+07:00  INFO 90416 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-07T15:39:52.807+07:00  WARN 90416 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-07T15:39:52.922+07:00  INFO 90416 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 07/08/2025@15:30:00+0700 to 07/08/2025@15:45:00+0700
2025-08-07T15:39:52.922+07:00  INFO 90416 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 07/08/2025@15:30:00+0700 to 07/08/2025@15:45:00+0700
2025-08-07T15:39:53.366+07:00  INFO 90416 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-07T15:39:53.366+07:00  INFO 90416 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-07T15:39:53.367+07:00  WARN 90416 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-07T15:39:53.592+07:00  INFO 90416 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-07T15:39:53.592+07:00  INFO 90416 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-07T15:39:53.592+07:00  INFO 90416 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-07T15:39:53.592+07:00  INFO 90416 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-07T15:39:53.592+07:00  INFO 90416 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-07T15:39:55.211+07:00  WARN 90416 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: a2a4e67a-46e2-4069-b815-e3703dde1cd0

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-07T15:39:55.214+07:00  INFO 90416 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-07T15:39:55.524+07:00  INFO 90416 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-07T15:39:55.525+07:00  INFO 90416 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-07T15:39:55.525+07:00  INFO 90416 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-07T15:39:55.525+07:00  INFO 90416 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-07T15:39:55.525+07:00  INFO 90416 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-07T15:39:55.525+07:00  INFO 90416 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-07T15:39:55.525+07:00  INFO 90416 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-07T15:39:55.525+07:00  INFO 90416 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-07T15:39:55.525+07:00  INFO 90416 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-07T15:39:55.525+07:00  INFO 90416 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-07T15:39:55.525+07:00  INFO 90416 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-07T15:39:55.525+07:00  INFO 90416 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-07T15:39:55.529+07:00  INFO 90416 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-07T15:39:55.529+07:00  INFO 90416 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-07T15:39:55.529+07:00  INFO 90416 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-07T15:39:55.580+07:00  INFO 90416 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07T15:39:55.580+07:00  INFO 90416 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-07T15:39:55.582+07:00  INFO 90416 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-07T15:39:55.590+07:00  INFO 90416 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@4acb19da{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-07T15:39:55.591+07:00  INFO 90416 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-07T15:39:55.591+07:00  INFO 90416 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-07T15:39:55.621+07:00  INFO 90416 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-07T15:39:55.621+07:00  INFO 90416 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-07T15:39:55.627+07:00  INFO 90416 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.086 seconds (process running for 14.323)
2025-08-07T15:39:58.892+07:00  INFO 90416 --- [qtp2072260217-35] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-07T15:40:02.551+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:40:02.554+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T15:40:06.543+07:00  INFO 90416 --- [qtp2072260217-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0i02rcm0mz2biew12esfg1uzv1
2025-08-07T15:40:06.543+07:00  INFO 90416 --- [qtp2072260217-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node012so9vcxcclgwgp6fjut6azwu0
2025-08-07T15:40:06.686+07:00  INFO 90416 --- [qtp2072260217-40] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:40:06.689+07:00  INFO 90416 --- [qtp2072260217-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0i02rcm0mz2biew12esfg1uzv1, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:40:07.124+07:00  INFO 90416 --- [qtp2072260217-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:40:07.130+07:00  INFO 90416 --- [qtp2072260217-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:40:58.701+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-08-07T15:40:58.723+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T15:41:05.735+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:41:34.935+07:00  INFO 90416 --- [qtp2072260217-36] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:41:34.942+07:00  INFO 90416 --- [qtp2072260217-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:41:34.960+07:00  INFO 90416 --- [qtp2072260217-64] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:41:34.978+07:00  INFO 90416 --- [qtp2072260217-64] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:41:46.819+07:00  INFO 90416 --- [qtp2072260217-64] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:41:46.820+07:00  INFO 90416 --- [qtp2072260217-40] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:41:46.826+07:00  INFO 90416 --- [qtp2072260217-64] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:41:46.828+07:00  INFO 90416 --- [qtp2072260217-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:42:06.840+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:42:57.966+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-07T15:42:57.983+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T15:43:04.993+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:44:06.058+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:45:02.214+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-08-07T15:45:02.235+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T15:45:04.245+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T15:45:04.247+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-07T15:45:04.250+07:00  INFO 90416 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 07/08/2025@15:45:04+0700
2025-08-07T15:45:04.271+07:00  INFO 90416 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 07/08/2025@15:45:00+0700 to 07/08/2025@16:00:00+0700
2025-08-07T15:45:04.271+07:00  INFO 90416 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 07/08/2025@15:45:00+0700 to 07/08/2025@16:00:00+0700
2025-08-07T15:45:04.272+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:46:06.609+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:47:01.735+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-07T15:47:01.746+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T15:47:03.755+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:47:57.728+07:00  INFO 90416 --- [qtp2072260217-40] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:47:57.739+07:00  INFO 90416 --- [qtp2072260217-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:47:57.784+07:00  INFO 90416 --- [qtp2072260217-41] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:47:57.799+07:00  INFO 90416 --- [qtp2072260217-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:47:57.847+07:00 ERROR 90416 --- [qtp2072260217-71] n.d.m.monitor.call.EndpointCallContext   : Start call with component DTableService, method searchDTables, arguments
[ {
  "tenantId" : "default",
  "companyId" : 4,
  "companyParentId" : null,
  "companyCode" : "bee",
  "companyLabel" : "Bee Corp",
  "companyFullName" : "BEE LOGISTICS CORPORATION",
  "loginId" : "nhat.le",
  "accountId" : 2089,
  "token" : "5ebaccaee21ec9256a40c394131a5379",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node012so9vcxcclgwgp6fjut6azwu0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 6213,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 10447,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Group",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 6142,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 8581,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2713,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2966,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2967,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2968,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2969,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 3739,
    "appId" : 12,
    "appModule" : "spreadsheet",
    "appName" : "spreadsheet",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11549,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12461,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 13328,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13329,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 13393,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14692,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14846,
    "appId" : 7,
    "appModule" : "company",
    "appName" : "company-asset",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 15054,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 4,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  } ],
  "attributes" : { },
  "clientId" : "default:nhat.le"
}, null, {
  "params" : { },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ ],
  "orderBy" : {
    "fields" : [ "modifiedTime", "createdTime" ],
    "selectFields" : [ "createdTime" ],
    "sort" : "DESC"
  },
  "maxReturn" : 1000
} ]
2025-08-07T15:47:57.854+07:00 ERROR 90416 --- [qtp2072260217-71] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.dtable.DTableLogic.searchDTables(DTableLogic.java:137)
	at net.datatp.module.dtable.DTableService.searchDTables(DTableService.java:62)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.dtable.DTableService$$SpringCGLIB$$0.searchDTables(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-08-07T15:47:57.893+07:00  INFO 90416 --- [qtp2072260217-71] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint DTableService/searchDTables
2025-08-07T15:48:01.465+07:00  INFO 90416 --- [qtp2072260217-69] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:48:01.466+07:00  INFO 90416 --- [qtp2072260217-68] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:48:01.479+07:00  INFO 90416 --- [qtp2072260217-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:48:01.482+07:00  INFO 90416 --- [qtp2072260217-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:48:06.866+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:49:02.014+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-08-07T15:49:02.031+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T15:49:03.033+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:49:23.749+07:00  INFO 90416 --- [qtp2072260217-68] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:49:23.754+07:00  INFO 90416 --- [qtp2072260217-36] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:49:23.787+07:00  INFO 90416 --- [qtp2072260217-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:49:23.787+07:00  INFO 90416 --- [qtp2072260217-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:49:29.893+07:00  INFO 90416 --- [qtp2072260217-40] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:49:29.903+07:00  INFO 90416 --- [qtp2072260217-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:49:29.917+07:00  INFO 90416 --- [qtp2072260217-62] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:49:29.923+07:00  INFO 90416 --- [qtp2072260217-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:49:39.695+07:00  INFO 90416 --- [qtp2072260217-68] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:49:39.721+07:00  INFO 90416 --- [qtp2072260217-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:49:39.743+07:00  INFO 90416 --- [qtp2072260217-60] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:49:39.774+07:00  INFO 90416 --- [qtp2072260217-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:50:06.987+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:50:06.990+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T15:50:07.602+07:00  INFO 90416 --- [qtp2072260217-62] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:50:07.603+07:00  INFO 90416 --- [qtp2072260217-60] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:50:07.615+07:00  INFO 90416 --- [qtp2072260217-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:50:07.615+07:00  INFO 90416 --- [qtp2072260217-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:50:44.332+07:00  INFO 90416 --- [Scheduler-1229735211-1] n.d.m.session.AppHttpSessionListener     : The session node0i02rcm0mz2biew12esfg1uzv1 is destroyed.
2025-08-07T15:51:02.187+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 3
2025-08-07T15:51:02.196+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T15:51:03.202+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:51:09.456+07:00  INFO 90416 --- [qtp2072260217-71] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint DTableService/searchDTables
2025-08-07T15:51:09.456+07:00  INFO 90416 --- [qtp2072260217-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint DTableService/searchDTables
2025-08-07T15:51:09.527+07:00  INFO 90416 --- [qtp2072260217-61] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-07T15:51:10.897+07:00  INFO 90416 --- [qtp2072260217-69] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:51:10.898+07:00  INFO 90416 --- [qtp2072260217-60] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:51:10.908+07:00  INFO 90416 --- [qtp2072260217-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:51:10.911+07:00  INFO 90416 --- [qtp2072260217-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:52:06.313+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:53:02.462+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 1
2025-08-07T15:53:02.510+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T15:53:02.510+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:53:03.270+07:00  INFO 90416 --- [qtp2072260217-62] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:53:03.310+07:00  INFO 90416 --- [qtp2072260217-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:53:03.350+07:00  INFO 90416 --- [qtp2072260217-85] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:53:03.359+07:00  INFO 90416 --- [qtp2072260217-85] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:53:03.696+07:00  INFO 90416 --- [qtp2072260217-85] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:53:03.700+07:00  INFO 90416 --- [qtp2072260217-85] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:53:03.757+07:00  INFO 90416 --- [qtp2072260217-61] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 5ebaccaee21ec9256a40c394131a5379
2025-08-07T15:53:03.764+07:00  INFO 90416 --- [qtp2072260217-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T15:53:39.152+07:00  INFO 90416 --- [qtp2072260217-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-08-07T15:53:42.845+07:00  INFO 90416 --- [qtp2072260217-92] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 316a1f13c0dfad7f8609fe80cba039ff
2025-08-07T15:53:42.863+07:00  INFO 90416 --- [qtp2072260217-92] n.d.m.c.a.CompanyAuthenticationService   : User tainv is logged in successfully system
2025-08-07T15:54:05.620+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:54:05.727+07:00  INFO 90416 --- [qtp2072260217-92] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-07T15:54:23.209+07:00  INFO 90416 --- [qtp2072260217-92] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 316a1f13c0dfad7f8609fe80cba039ff
2025-08-07T15:54:23.211+07:00  INFO 90416 --- [qtp2072260217-36] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 316a1f13c0dfad7f8609fe80cba039ff
2025-08-07T15:54:23.237+07:00  INFO 90416 --- [qtp2072260217-92] n.d.m.c.a.CompanyAuthenticationService   : User tainv is logged in successfully system
2025-08-07T15:54:23.237+07:00  INFO 90416 --- [qtp2072260217-36] n.d.m.c.a.CompanyAuthenticationService   : User tainv is logged in successfully system
2025-08-07T15:54:24.712+07:00  INFO 90416 --- [qtp2072260217-61] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 316a1f13c0dfad7f8609fe80cba039ff
2025-08-07T15:54:24.713+07:00  INFO 90416 --- [qtp2072260217-69] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 316a1f13c0dfad7f8609fe80cba039ff
2025-08-07T15:54:24.723+07:00  INFO 90416 --- [qtp2072260217-61] n.d.m.c.a.CompanyAuthenticationService   : User tainv is logged in successfully system
2025-08-07T15:54:24.724+07:00  INFO 90416 --- [qtp2072260217-69] n.d.m.c.a.CompanyAuthenticationService   : User tainv is logged in successfully system
2025-08-07T15:55:01.785+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-08-07T15:55:01.797+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T15:55:06.802+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:55:06.807+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T15:56:04.906+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:57:01.004+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 1
2025-08-07T15:57:01.020+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T15:57:07.025+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:58:04.127+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T15:59:00.247+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-07T15:59:00.269+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T15:59:06.278+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:00:03.373+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-07T16:00:03.380+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:00:03.380+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T16:00:03.381+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-07T16:00:03.386+07:00  INFO 90416 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 07/08/2025@16:00:03+0700
2025-08-07T16:00:03.435+07:00  INFO 90416 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 07/08/2025@16:00:00+0700 to 07/08/2025@16:15:00+0700
2025-08-07T16:00:03.435+07:00  INFO 90416 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 07/08/2025@16:00:00+0700 to 07/08/2025@16:15:00+0700
2025-08-07T16:00:35.903+07:00  INFO 90416 --- [qtp2072260217-36] n.d.m.c.a.CompanyAuthenticationService   : User tainv logout successfully 
2025-08-07T16:00:37.423+07:00  INFO 90416 --- [qtp2072260217-92] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = f217bcafb5bb3f000912906cdbb0a6cd
2025-08-07T16:00:37.432+07:00  INFO 90416 --- [qtp2072260217-92] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T16:00:59.556+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-08-07T16:00:59.587+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T16:01:06.598+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:01:13.750+07:00  INFO 90416 --- [qtp2072260217-92] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-08-07T16:01:16.017+07:00  INFO 90416 --- [qtp2072260217-68] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 9845312a2eb3434fcf1c18ba221b19d8
2025-08-07T16:01:16.027+07:00  INFO 90416 --- [qtp2072260217-68] n.d.m.c.a.CompanyAuthenticationService   : User hcmhanhnt is logged in successfully system
2025-08-07T16:01:30.749+07:00  INFO 90416 --- [qtp2072260217-68] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-07T16:02:02.691+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:02:33.730+07:00  INFO 90416 --- [qtp2072260217-36] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 9845312a2eb3434fcf1c18ba221b19d8
2025-08-07T16:02:33.750+07:00  INFO 90416 --- [qtp2072260217-36] n.d.m.c.a.CompanyAuthenticationService   : User hcmhanhnt is logged in successfully system
2025-08-07T16:02:33.773+07:00  INFO 90416 --- [qtp2072260217-71] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 9845312a2eb3434fcf1c18ba221b19d8
2025-08-07T16:02:33.778+07:00  INFO 90416 --- [qtp2072260217-71] n.d.m.c.a.CompanyAuthenticationService   : User hcmhanhnt is logged in successfully system
2025-08-07T16:02:46.055+07:00  INFO 90416 --- [qtp2072260217-36] n.d.m.c.a.CompanyAuthenticationService   : User hcmhanhnt logout successfully 
2025-08-07T16:02:58.823+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 4
2025-08-07T16:02:58.848+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T16:03:05.862+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:03:28.715+07:00  INFO 90416 --- [qtp2072260217-39] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-07T16:04:06.963+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:04:30.745+07:00  INFO 90416 --- [qtp2072260217-36] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 9845312a2eb3434fcf1c18ba221b19d8
2025-08-07T16:04:30.769+07:00  INFO 90416 --- [qtp2072260217-36] n.d.m.c.a.CompanyAuthenticationService   : User hcmhanhnt is logged in successfully system
2025-08-07T16:04:30.790+07:00  INFO 90416 --- [qtp2072260217-39] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = 9845312a2eb3434fcf1c18ba221b19d8
2025-08-07T16:04:30.795+07:00  INFO 90416 --- [qtp2072260217-39] n.d.m.c.a.CompanyAuthenticationService   : User hcmhanhnt is logged in successfully system
2025-08-07T16:04:58.091+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 23, expire count 10
2025-08-07T16:04:58.093+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T16:05:05.105+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:05:05.107+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T16:05:07.880+07:00 ERROR 90416 --- [qtp2072260217-39] net.datatp.module.account.AccountLogic   : User hcmhanhnt try to login into system, but fail
2025-08-07T16:05:07.882+07:00 ERROR 90416 --- [qtp2072260217-39] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method authenticate, arguments
[ {
  "tenantId" : "",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node012so9vcxcclgwgp6fjut6azwu0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : ":anon"
}, null, {
  "loginId" : "hcmhanhnt",
  "authorization" : null,
  "company" : "",
  "password" : "*********",
  "timeToLiveInMin" : 10080,
  "accessType" : "Employee"
} ]
2025-08-07T16:05:07.882+07:00 ERROR 90416 --- [qtp2072260217-39] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:84)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-08-07T16:05:07.884+07:00  INFO 90416 --- [qtp2072260217-39] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/authenticate
2025-08-07T16:05:12.176+07:00  INFO 90416 --- [qtp2072260217-69] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = cf075911a99c62b4d21464c45a44e2be
2025-08-07T16:05:12.180+07:00  INFO 90416 --- [qtp2072260217-69] n.d.m.c.a.CompanyAuthenticationService   : User hcmhanhnt is logged in successfully system
2025-08-07T16:05:33.711+07:00  INFO 90416 --- [qtp2072260217-84] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-07T16:06:06.203+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:06:35.724+07:00  INFO 90416 --- [qtp2072260217-39] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = cf075911a99c62b4d21464c45a44e2be
2025-08-07T16:06:35.742+07:00  INFO 90416 --- [qtp2072260217-39] n.d.m.c.a.CompanyAuthenticationService   : User hcmhanhnt is logged in successfully system
2025-08-07T16:06:35.767+07:00  INFO 90416 --- [qtp2072260217-65] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = cf075911a99c62b4d21464c45a44e2be
2025-08-07T16:06:35.771+07:00  INFO 90416 --- [qtp2072260217-65] n.d.m.c.a.CompanyAuthenticationService   : User hcmhanhnt is logged in successfully system
2025-08-07T16:07:02.305+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-08-07T16:07:02.331+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T16:07:04.338+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:07:16.699+07:00  INFO 90416 --- [qtp2072260217-65] n.d.m.c.a.CompanyAuthenticationService   : User hcmhanhnt logout successfully 
2025-08-07T16:07:18.681+07:00  INFO 90416 --- [qtp2072260217-69] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = c83af5e8df6717a26d837e45f928aaf7
2025-08-07T16:07:18.688+07:00  INFO 90416 --- [qtp2072260217-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T16:07:38.426+07:00  INFO 90416 --- [qtp2072260217-68] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = c83af5e8df6717a26d837e45f928aaf7
2025-08-07T16:07:38.434+07:00  INFO 90416 --- [qtp2072260217-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T16:07:38.458+07:00  INFO 90416 --- [qtp2072260217-84] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = c83af5e8df6717a26d837e45f928aaf7
2025-08-07T16:07:38.477+07:00  INFO 90416 --- [qtp2072260217-84] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T16:07:38.712+07:00  INFO 90416 --- [qtp2072260217-41] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = c83af5e8df6717a26d837e45f928aaf7
2025-08-07T16:07:38.718+07:00  INFO 90416 --- [qtp2072260217-92] n.d.module.session.ClientSessionManager  : Add a client session id = node012so9vcxcclgwgp6fjut6azwu0, token = c83af5e8df6717a26d837e45f928aaf7
2025-08-07T16:07:38.730+07:00  INFO 90416 --- [qtp2072260217-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T16:07:38.743+07:00  INFO 90416 --- [qtp2072260217-92] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T16:08:06.445+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:09:02.572+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-08-07T16:09:02.593+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T16:09:03.599+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:10:06.688+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:10:06.691+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T16:11:01.780+07:00  INFO 90416 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 7
2025-08-07T16:11:01.784+07:00  INFO 90416 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T16:11:02.790+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:12:05.911+07:00  INFO 90416 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T16:12:51.839+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@4acb19da{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-07T16:12:51.841+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-07T16:12:51.842+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-07T16:12:51.842+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-07T16:12:51.843+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-07T16:12:51.876+07:00  INFO 90416 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T16:12:51.960+07:00  INFO 90416 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-07T16:12:51.966+07:00  INFO 90416 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-07T16:12:51.991+07:00  INFO 90416 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T16:12:51.998+07:00  INFO 90416 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T16:12:52.009+07:00  INFO 90416 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T16:12:52.010+07:00  INFO 90416 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-07T16:12:52.011+07:00  INFO 90416 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-07T16:12:52.011+07:00  INFO 90416 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-07T16:12:52.012+07:00  INFO 90416 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-07T16:12:52.012+07:00  INFO 90416 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-07T16:12:52.200+07:00  INFO 90416 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-07T16:12:52.201+07:00  INFO 90416 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-07T16:12:52.202+07:00  INFO 90416 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-07T16:12:52.202+07:00  INFO 90416 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-07T16:12:52.203+07:00  INFO 90416 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-07T16:12:52.208+07:00  INFO 90416 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7fec354{STOPPING}[12.0.15,sto=0]
2025-08-07T16:12:52.213+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-07T16:12:52.215+07:00  INFO 90416 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@6f636de3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16479615897773935899/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@522fdf0c{STOPPED}}
