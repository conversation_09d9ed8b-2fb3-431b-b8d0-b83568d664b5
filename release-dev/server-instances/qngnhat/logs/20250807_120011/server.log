2025-08-07T12:00:12.701+07:00  INFO 66154 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 66154 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-07T12:00:12.702+07:00  INFO 66154 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-07T12:00:13.463+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.537+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 71 ms. Found 22 JPA repository interfaces.
2025-08-07T12:00:13.549+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.550+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-07T12:00:13.550+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.557+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-08-07T12:00:13.558+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.561+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-07T12:00:13.561+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.564+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-07T12:00:13.575+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.581+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-08-07T12:00:13.591+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.596+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-08-07T12:00:13.600+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.643+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 42 ms. Found 3 JPA repository interfaces.
2025-08-07T12:00:13.643+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.644+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-07T12:00:13.649+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.656+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-08-07T12:00:13.660+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.663+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-07T12:00:13.663+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.666+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-07T12:00:13.668+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.676+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-08-07T12:00:13.676+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.679+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-08-07T12:00:13.680+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.680+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-07T12:00:13.680+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.681+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-07T12:00:13.681+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.685+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-07T12:00:13.685+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.686+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-08-07T12:00:13.687+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.687+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-07T12:00:13.687+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.697+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-08-07T12:00:13.707+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.713+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-08-07T12:00:13.714+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.717+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-07T12:00:13.717+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.721+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-08-07T12:00:13.721+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.726+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-08-07T12:00:13.727+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.730+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-07T12:00:13.730+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.733+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-07T12:00:13.734+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.738+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-07T12:00:13.738+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.749+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 14 JPA repository interfaces.
2025-08-07T12:00:13.749+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.761+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 20 JPA repository interfaces.
2025-08-07T12:00:13.761+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.762+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-07T12:00:13.767+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.768+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-07T12:00:13.768+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.776+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-08-07T12:00:13.777+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.813+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 67 JPA repository interfaces.
2025-08-07T12:00:13.814+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.815+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-07T12:00:13.820+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-07T12:00:13.823+07:00  INFO 66154 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-07T12:00:14.050+07:00  INFO 66154 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-07T12:00:14.053+07:00  INFO 66154 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-07T12:00:14.329+07:00  WARN 66154 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-07T12:00:14.536+07:00  INFO 66154 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-07T12:00:14.538+07:00  INFO 66154 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-07T12:00:14.550+07:00  INFO 66154 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-07T12:00:14.550+07:00  INFO 66154 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1706 ms
2025-08-07T12:00:14.610+07:00  WARN 66154 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-07T12:00:14.611+07:00  INFO 66154 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-07T12:00:14.733+07:00  INFO 66154 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@230ea983
2025-08-07T12:00:14.733+07:00  INFO 66154 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-07T12:00:14.738+07:00  WARN 66154 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-07T12:00:14.738+07:00  INFO 66154 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-07T12:00:14.743+07:00  INFO 66154 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@5ac3d009
2025-08-07T12:00:14.743+07:00  INFO 66154 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-07T12:00:14.744+07:00  WARN 66154 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-07T12:00:14.744+07:00  INFO 66154 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-07T12:00:15.252+07:00  INFO 66154 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@48fbb1bc
2025-08-07T12:00:15.252+07:00  INFO 66154 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-07T12:00:15.252+07:00  WARN 66154 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-07T12:00:15.252+07:00  INFO 66154 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-07T12:00:15.285+07:00  INFO 66154 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@4d001a14
2025-08-07T12:00:15.285+07:00  INFO 66154 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-07T12:00:15.285+07:00  WARN 66154 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-07T12:00:15.286+07:00  INFO 66154 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-07T12:00:15.299+07:00  INFO 66154 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@312ddc51
2025-08-07T12:00:15.301+07:00  INFO 66154 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-07T12:00:15.302+07:00  INFO 66154 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-07T12:00:15.363+07:00  INFO 66154 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-07T12:00:15.366+07:00  INFO 66154 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@10587ce5{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.11746802776188121522/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@58581ab6{STARTED}}
2025-08-07T12:00:15.366+07:00  INFO 66154 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@10587ce5{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.11746802776188121522/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@58581ab6{STARTED}}
2025-08-07T12:00:15.368+07:00  INFO 66154 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@1828eff{STARTING}[12.0.15,sto=0] @3353ms
2025-08-07T12:00:15.437+07:00  INFO 66154 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-07T12:00:15.468+07:00  INFO 66154 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-07T12:00:15.483+07:00  INFO 66154 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-07T12:00:15.611+07:00  INFO 66154 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-07T12:00:15.659+07:00  WARN 66154 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-07T12:00:16.294+07:00  INFO 66154 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-07T12:00:16.302+07:00  INFO 66154 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@50060b19] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-07T12:00:16.452+07:00  INFO 66154 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T12:00:16.710+07:00  INFO 66154 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-08-07T12:00:16.712+07:00  INFO 66154 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-07T12:00:16.718+07:00  INFO 66154 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-07T12:00:16.719+07:00  INFO 66154 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-07T12:00:16.745+07:00  INFO 66154 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-07T12:00:16.760+07:00  WARN 66154 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-07T12:00:18.909+07:00  INFO 66154 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-07T12:00:18.910+07:00  INFO 66154 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@731f409f] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-07T12:00:19.319+07:00  WARN 66154 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-07T12:00:19.319+07:00  WARN 66154 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-07T12:00:19.328+07:00  WARN 66154 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-07T12:00:19.328+07:00  WARN 66154 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-07T12:00:19.344+07:00  WARN 66154 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-07T12:00:19.344+07:00  WARN 66154 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-07T12:00:19.867+07:00  INFO 66154 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T12:00:19.875+07:00  INFO 66154 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-07T12:00:19.876+07:00  INFO 66154 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-07T12:00:19.894+07:00  INFO 66154 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-07T12:00:19.903+07:00  WARN 66154 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-07T12:00:20.508+07:00  INFO 66154 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-07T12:00:20.511+07:00  INFO 66154 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@49207899] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-07T12:00:20.687+07:00  WARN 66154 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-07T12:00:20.687+07:00  WARN 66154 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-07T12:00:21.024+07:00  INFO 66154 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T12:00:21.057+07:00  INFO 66154 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-07T12:00:21.063+07:00  INFO 66154 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-07T12:00:21.063+07:00  INFO 66154 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-07T12:00:21.070+07:00  WARN 66154 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-07T12:00:21.207+07:00  INFO 66154 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-07T12:00:21.692+07:00  INFO 66154 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-07T12:00:21.694+07:00  INFO 66154 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-07T12:00:21.731+07:00  INFO 66154 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-07T12:00:21.776+07:00  INFO 66154 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-07T12:00:21.887+07:00  INFO 66154 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-07T12:00:21.923+07:00  INFO 66154 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-07T12:00:21.952+07:00  INFO 66154 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 169504863ms : this is harmless.
2025-08-07T12:00:21.963+07:00  INFO 66154 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-07T12:00:21.967+07:00  INFO 66154 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-07T12:00:21.992+07:00  INFO 66154 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1106859579ms : this is harmless.
2025-08-07T12:00:21.994+07:00  INFO 66154 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-07T12:00:22.010+07:00  INFO 66154 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-07T12:00:22.011+07:00  INFO 66154 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-07T12:00:24.183+07:00  INFO 66154 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-07T12:00:24.183+07:00  INFO 66154 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-07T12:00:24.183+07:00  WARN 66154 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-07T12:00:24.310+07:00  INFO 66154 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 07/08/2025@12:00:00+0700 to 07/08/2025@12:15:00+0700
2025-08-07T12:00:24.310+07:00  INFO 66154 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 07/08/2025@12:00:00+0700 to 07/08/2025@12:15:00+0700
2025-08-07T12:00:24.811+07:00  INFO 66154 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-07T12:00:24.811+07:00  INFO 66154 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-07T12:00:24.811+07:00  WARN 66154 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-07T12:00:25.225+07:00  INFO 66154 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-07T12:00:25.226+07:00  INFO 66154 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-07T12:00:25.226+07:00  INFO 66154 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-07T12:00:25.226+07:00  INFO 66154 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-07T12:00:25.226+07:00  INFO 66154 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-07T12:00:26.856+07:00  WARN 66154 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: f1e992e5-842b-433f-9de7-d861529a114e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-07T12:00:26.860+07:00  INFO 66154 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-07T12:00:27.205+07:00  INFO 66154 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-07T12:00:27.205+07:00  INFO 66154 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-07T12:00:27.205+07:00  INFO 66154 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-07T12:00:27.205+07:00  INFO 66154 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-07T12:00:27.205+07:00  INFO 66154 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-07T12:00:27.205+07:00  INFO 66154 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-07T12:00:27.205+07:00  INFO 66154 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-07T12:00:27.205+07:00  INFO 66154 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-07T12:00:27.206+07:00  INFO 66154 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-07T12:00:27.206+07:00  INFO 66154 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-07T12:00:27.206+07:00  INFO 66154 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-07T12:00:27.206+07:00  INFO 66154 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-07T12:00:27.210+07:00  INFO 66154 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-07T12:00:27.210+07:00  INFO 66154 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-07T12:00:27.210+07:00  INFO 66154 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-07T12:00:27.299+07:00  INFO 66154 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07T12:00:27.300+07:00  INFO 66154 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-07T12:00:27.301+07:00  INFO 66154 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-07T12:00:27.310+07:00  INFO 66154 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@de25cf2{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-07T12:00:27.311+07:00  INFO 66154 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-07T12:00:27.312+07:00  INFO 66154 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-07T12:00:27.339+07:00  INFO 66154 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-07T12:00:27.339+07:00  INFO 66154 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-07T12:00:27.346+07:00  INFO 66154 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.995 seconds (process running for 15.331)
2025-08-07T12:00:46.234+07:00  INFO 66154 --- [qtp1851920297-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0bvpuswy0tiho1hte5tupmju5t0
2025-08-07T12:00:46.234+07:00  INFO 66154 --- [qtp1851920297-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0dafciscnjnjt1w2vt38cofdzp1
2025-08-07T12:00:46.453+07:00  INFO 66154 --- [qtp1851920297-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0bvpuswy0tiho1hte5tupmju5t0, token = 0e54ebd837bc41e9ab7f02815c52b90d
2025-08-07T12:00:46.454+07:00  INFO 66154 --- [qtp1851920297-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0dafciscnjnjt1w2vt38cofdzp1, token = 0e54ebd837bc41e9ab7f02815c52b90d
2025-08-07T12:00:46.980+07:00  INFO 66154 --- [qtp1851920297-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T12:00:46.980+07:00  INFO 66154 --- [qtp1851920297-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T12:01:02.293+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:01:11.442+07:00  INFO 66154 --- [qtp1851920297-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0dafciscnjnjt1w2vt38cofdzp1, token = 0e54ebd837bc41e9ab7f02815c52b90d
2025-08-07T12:01:11.459+07:00  INFO 66154 --- [qtp1851920297-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T12:01:11.465+07:00  INFO 66154 --- [qtp1851920297-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0dafciscnjnjt1w2vt38cofdzp1, token = 0e54ebd837bc41e9ab7f02815c52b90d
2025-08-07T12:01:11.498+07:00  INFO 66154 --- [qtp1851920297-67] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T12:01:30.417+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-07T12:01:30.464+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:02:05.524+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:03:06.617+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:03:29.732+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-07T12:03:29.734+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:04:04.825+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:05:07.024+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:05:07.027+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T12:05:34.146+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-07T12:05:34.164+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:06:04.264+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:07:06.460+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:07:33.529+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:07:33.533+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:08:03.625+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:09:06.804+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:09:33.897+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-07T12:09:33.902+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:10:02.999+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:10:03.000+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T12:10:15.388+07:00  INFO 66154 --- [Scheduler-2124114117-1] n.d.m.session.AppHttpSessionListener     : The session node0bvpuswy0tiho1hte5tupmju5t0 is destroyed.
2025-08-07T12:10:18.582+07:00  INFO 66154 --- [qtp1851920297-61] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-07T12:11:05.593+07:00  INFO 66154 --- [qtp1851920297-40] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-07T12:11:06.198+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:11:21.606+07:00  INFO 66154 --- [qtp1851920297-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0dafciscnjnjt1w2vt38cofdzp1, token = 0e54ebd837bc41e9ab7f02815c52b90d
2025-08-07T12:11:21.669+07:00  INFO 66154 --- [qtp1851920297-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T12:11:21.669+07:00  INFO 66154 --- [qtp1851920297-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0dafciscnjnjt1w2vt38cofdzp1, token = 0e54ebd837bc41e9ab7f02815c52b90d
2025-08-07T12:11:21.676+07:00  INFO 66154 --- [qtp1851920297-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T12:11:34.299+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 4
2025-08-07T12:11:34.317+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:12:02.399+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:12:08.587+07:00  INFO 66154 --- [qtp1851920297-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0dafciscnjnjt1w2vt38cofdzp1, token = 0e54ebd837bc41e9ab7f02815c52b90d
2025-08-07T12:12:08.588+07:00  INFO 66154 --- [qtp1851920297-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0dafciscnjnjt1w2vt38cofdzp1, token = 0e54ebd837bc41e9ab7f02815c52b90d
2025-08-07T12:12:08.598+07:00  INFO 66154 --- [qtp1851920297-67] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T12:12:08.598+07:00  INFO 66154 --- [qtp1851920297-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-07T12:13:05.561+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:13:33.627+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:13:33.633+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:14:06.730+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:15:04.885+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:15:04.886+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T12:15:04.887+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-07T12:15:04.893+07:00  INFO 66154 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 07/08/2025@12:15:04+0700
2025-08-07T12:15:04.943+07:00  INFO 66154 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 07/08/2025@12:15:00+0700 to 07/08/2025@12:30:00+0700
2025-08-07T12:15:04.943+07:00  INFO 66154 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 07/08/2025@12:15:00+0700 to 07/08/2025@12:30:00+0700
2025-08-07T12:15:33.037+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-08-07T12:15:33.043+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:16:06.119+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:17:04.285+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:17:32.368+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:17:32.369+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:18:06.468+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:19:03.631+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:19:31.698+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-07T12:19:31.702+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:20:06.800+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:20:06.800+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T12:21:02.966+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:21:31.046+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-07T12:21:31.047+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:22:06.151+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:23:02.306+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:23:30.406+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-08-07T12:23:30.410+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:24:05.474+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:25:06.620+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T12:25:06.622+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:25:29.702+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-07T12:25:29.705+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:26:05.081+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:27:06.250+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:27:34.337+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-07T12:27:34.355+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:28:04.425+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:29:06.592+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:29:33.644+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:29:33.647+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:30:03.734+07:00  INFO 66154 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 07/08/2025@12:30:03+0700
2025-08-07T12:30:03.749+07:00  INFO 66154 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 07/08/2025@12:30:00+0700 to 07/08/2025@12:45:00+0700
2025-08-07T12:30:03.749+07:00  INFO 66154 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 07/08/2025@12:30:00+0700 to 07/08/2025@12:45:00+0700
2025-08-07T12:30:03.749+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-07T12:30:03.750+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:30:03.750+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T12:31:06.922+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:31:34.016+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-07T12:31:34.021+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:32:03.103+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:33:06.314+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:33:33.389+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:33:33.392+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:34:02.482+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:35:05.668+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:35:05.670+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T12:35:33.730+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-07T12:35:33.736+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:36:06.827+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:37:04.992+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:37:33.072+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:37:33.075+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:38:06.160+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:39:04.273+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:39:32.326+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-07T12:39:32.357+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:40:06.412+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:40:06.418+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-07T12:41:03.509+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:41:31.575+07:00  INFO 66154 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-07T12:41:31.586+07:00  INFO 66154 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:42:06.648+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:43:02.641+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@de25cf2{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-07T12:43:02.642+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-07T12:43:02.642+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-07T12:43:02.642+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-07T12:43:02.643+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-07T12:43:02.658+07:00  INFO 66154 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-07T12:43:02.748+07:00  INFO 66154 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-07T12:43:02.753+07:00  INFO 66154 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-07T12:43:02.776+07:00  INFO 66154 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T12:43:02.777+07:00  INFO 66154 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T12:43:02.778+07:00  INFO 66154 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-07T12:43:02.779+07:00  INFO 66154 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-07T12:43:02.780+07:00  INFO 66154 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-07T12:43:02.780+07:00  INFO 66154 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-07T12:43:02.780+07:00  INFO 66154 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-07T12:43:02.780+07:00  INFO 66154 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-07T12:43:02.922+07:00  INFO 66154 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-07T12:43:02.922+07:00  INFO 66154 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-07T12:43:02.923+07:00  INFO 66154 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-07T12:43:02.923+07:00  INFO 66154 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-07T12:43:02.924+07:00  INFO 66154 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-07T12:43:02.928+07:00  INFO 66154 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@1828eff{STOPPING}[12.0.15,sto=0]
2025-08-07T12:43:02.936+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-07T12:43:02.938+07:00  INFO 66154 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@10587ce5{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.11746802776188121522/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@58581ab6{STOPPED}}
