package migration.server.crm

import cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.data.db.entity.ICompany
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.security.client.ClientContext
import net.datatp.util.ds.MapObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class UpdateDataEmployees extends ServiceRunnableSet {
    private static final Logger log = LoggerFactory.getLogger(UpdateDataEmployees.class);
    private static String label = "------------UPDATE DATA EMPLOYEE--------------"

    public UpdateDataEmployees() {
        super(label);
         ServiceRunnable syncService = new ServiceRunnable(label) {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
              ClientContext client = scriptCtx.getClientCtx()
              ICompany company = scriptCtx.getCompany();
              BFSOneCRMLogic crmLogic = scriptCtx.getService(BFSOneCRMLogic.class);
              List<MapObject> all = crmLogic.loadUsers(client);


              /*
              {
                "UserID" : "CT1799",
                "FullName" : "NGÔ CHÍ BẢO - HCMBAONC",
                "Username" : "HCMBAONC",
                "Position" : "LOG",
                "DeptID" : "DP010",
                "DepartmentName" : "LOGISTICS / BEEHCM",
                "CmpID" : "BEEHCM",
                "CmpName" : "BEE LOGISTICS CORPORATION",
                "Email" : "<EMAIL>",
                "HrmCode" : "BEEHCM00910",
                "Phone" : "+84792627513"
              }
               */
              for(MapObject user : all) {
                /*
                    get username from user
                    find employee by username
                    if employee is not found, dump to console.
                 */
              }

            }
        };
        addRunnable(syncService);
    }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-06-19");

UpdateDataEmployees updateEmployee = new UpdateDataEmployees();
updateEmployee.run(reporter, scriptCtx);
return "DONE!!!";