package migration.db.common

import java.sql.Connection

import org.slf4j.Logger

import lib.data.DBMigrationRunnable
import lib.data.DBMigrationRunnableSet
import lib.data.RunnableReporter
import net.datatp.cli.ShellApplicationContext
import net.datatp.module.data.db.util.DBConnectionUtil

public class AlterTableCommonSet extends DBMigrationRunnableSet {
    public AlterTableCommonSet() {
        super("""Alter Common Tables""");

    String label = """Alter Common Tables""";
    DBMigrationRunnable alterTables = new DBMigrationRunnable(label) {
        @Override
        public void run(RunnableReporter reporter, DBConnectionUtil connUtil) {
          //Communication
          //add new column
          connUtil.execute("""
            ALTER TABLE message_message ADD COLUMN sender_account_id BIGINT;
            ALTER TABLE message_recipient_message ADD COLUMN sender_account_id BIGINT;
            ALTER TABLE message_recipient_message ADD COLUMN recipient_account_id BIGINT;
            ALTER TABLE message_target_recipient ADD COLUMN recipient_account_id BIGINT;
            ALTER TABLE message_target_recipient ADD COLUMN recipient_email varchar;
          """);
          
          //update value
          int batchSize = 300;
          int limit = 80000;
          for (int i = 1; i < limit; i += batchSize) {
            int endIndex = Math.min(i + batchSize, limit);
            connUtil.execute("""
              UPDATE message_message mm
              SET sender_account_id = (
                  SELECT a.id
                  FROM account_account a
                  WHERE a.login_id = mm.sender_login_id AND a.storage_state = 'ACTIVE'
              )
              WHERE mm.sender_login_id IS NOT NULL
              AND mm.id BETWEEN ${i} AND ${endIndex};
            """);
          }
          
          batchSize = 300;
          limit = 50000;
          for (int i = 1; i < limit; i += batchSize) {
            int endIndex = Math.min(i + batchSize, limit);
            connUtil.execute("""
              UPDATE message_recipient_message mrm
              SET sender_account_id = (
                  SELECT a.id
                  FROM account_account a
                  WHERE LOWER(a.login_id) = LOWER(mrm.sender_id)
                  AND a.storage_state = 'ACTIVE'
                  LIMIT 1
              )
              WHERE mrm.id BETWEEN ${i} AND ${endIndex};
            """);
          }
          
          batchSize = 300;
          limit = 50000;
          for (int i = 1; i < limit; i += batchSize) {
            int endIndex = Math.min(i + batchSize, limit);
            connUtil.execute("""
              UPDATE message_recipient_message mrm
              SET recipient_account_id = (
                  SELECT a.id
                  FROM account_account a
                  WHERE LOWER(a.login_id) = LOWER(mrm.recipient_id)
                  AND a.storage_state = 'ACTIVE'
                  LIMIT 1
              )
              WHERE mrm.sender_id IS NOT NULL AND mrm.recipient_id IS NOT NULL
              AND mrm.id BETWEEN ${i} AND ${endIndex};
            """);
          }
          
          batchSize = 300;
          limit = 165000;
          for (int i = 1; i < limit; i += batchSize) {
            int endIndex = Math.min(i + batchSize, limit);
            connUtil.execute("""
              UPDATE message_target_recipient mtr
              SET recipient_account_id = (
                  SELECT a.id
                  FROM account_account a
                  WHERE LOWER(a.login_id) = LOWER(mtr.recipient_id)
                  AND a.storage_state = 'ACTIVE'
                  LIMIT 1
              )
              WHERE mtr.recipient_id IS NOT NULL
              AND mtr.deliver_type IN ('Private', 'Channel')
              AND mtr.id BETWEEN ${i} AND ${endIndex};
            """);
          }
          
          batchSize = 400;
          limit = 165000;
          for (int i = 1; i < limit; i += batchSize) {
            int endIndex = Math.min(i + batchSize, limit);
            connUtil.execute("""
              UPDATE message_target_recipient mtr
              SET recipient_email = recipient_id
              WHERE mtr.recipient_id IS NOT NULL
              AND mtr.deliver_type IN ('Email', 'CCEmail')
              AND mtr.id BETWEEN ${i} AND ${endIndex};
            """);
          }
          
          batchSize = 300;
          limit = 65000;
          for (int i = 1; i < limit; i += batchSize) {
            int endIndex = Math.min(i + batchSize, limit);
            connUtil.execute("""
              UPDATE communication_account ca
              SET account_id = (
                  SELECT a.id
                  FROM account_account a
                  WHERE LOWER(a.login_id) = LOWER(ca.login_id)
                  AND a.storage_state = 'ACTIVE'
                  LIMIT 1
              )
              WHERE ca.id BETWEEN ${i} AND ${endIndex};
            """);
          }
          
          //delete un-used data
          connUtil.execute("""
            DELETE FROM message_message WHERE sender_account_id IS NULL;
            DELETE FROM message_recipient_message WHERE sender_account_id IS NULL OR recipient_account_id IS NULL;
            DELETE FROM message_target_recipient WHERE recipient_account_id IS NULL AND recipient_email IS NULL;
            DELETE FROM communication_account WHERE account_id IS NULL;
          """);
          
          //alter column
          connUtil.execute("""
            ALTER TABLE message_message
            ALTER COLUMN sender_account_id SET NOT NULL;
            
            ALTER TABLE message_recipient_message
            ALTER COLUMN sender_account_id SET NOT NULL,
            ALTER COLUMN recipient_account_id SET NOT NULL;
            
            ALTER TABLE communication_account
            ALTER COLUMN account_id SET NOT NULL;
          """);
          
          //drop un-used column
          connUtil.execute("""
            ALTER TABLE message_message DROP COLUMN sender_login_id;
            ALTER TABLE message_recipient_message DROP COLUMN recipient_id;
            ALTER TABLE message_recipient_message DROP COLUMN sender_id;
            ALTER TABLE communication_account DROP COLUMN login_id;
            ALTER TABLE message_target_recipient DROP COLUMN recipient_id;
          """);
          
          //Company
          connUtil.execute("ALTER TABLE company_company DROP COLUMN account_admin_login_id;");
          
          //D-table
          connUtil.execute("ALTER TABLE data_dtable ADD COLUMN owner_account_id BIGINT;");
          connUtil.execute("ALTER TABLE data_dtable_row ADD COLUMN owner_account_id BIGINT;");
          
          connUtil.execute("""
            UPDATE data_dtable d
            SET owner_account_id = (
                SELECT a.id
                FROM account_account a
                WHERE LOWER(a.login_id) = LOWER(d.owner)
                LIMIT 1
            );
          """);
          
          connUtil.execute("""
            UPDATE data_dtable d
            SET owner_label = (
                SELECT a.full_name
                FROM account_account a
                WHERE a.id = d.owner_account_id
            );
          """);

          batchSize = 10000;
          limit = 214942;
          for (int i = 1; i < limit; i += batchSize) {
            int endIndex = Math.min(i + batchSize, limit);
            connUtil.execute("""
              UPDATE data_dtable_row r
              SET dtable_id = (
                  SELECT d.id
                  FROM data_dtable d
                  WHERE d.code = r.spread_sheet_code 
                  LIMIT 1
              )
              WHERE r.id BETWEEN ${i} AND ${endIndex};
            """);
          }
          
          connUtil.execute("""
              UPDATE data_dtable_row d
              SET owner_account_id = (
              SELECT a.id
              FROM account_account a
              WHERE a.login_id = d.owner
              LIMIT 1
              );
              """);
          
          connUtil.execute("""
            UPDATE data_dtable_row d
            SET owner_account_id = (
                SELECT a.id
                FROM account_account a
                WHERE LOWER(a.login_id) = LOWER(d.owner)
                LIMIT 1
            )
            WHERE owner_account_id IS NULL;
          """);
          
          connUtil.execute("""
            UPDATE data_dtable_row d
            SET owner_label = (
                SELECT a.full_name
                FROM account_account a
                WHERE a.id = d.owner_account_id
            );
          """);
          
          connUtil.execute("""
            UPDATE data_dtable_permission d
            SET user_id = (
                SELECT a.id
                FROM account_account a
                WHERE LOWER(a.login_id) = LOWER(d.login_id)
            );
          """);
          
          connUtil.execute("""
            DELETE FROM data_dtable_cell c
            WHERE c.row_id IN (
              SELECT r.id from data_dtable_row r
              WHERE r.owner_account_id IS NULL OR r.dtable_id IS NULL
            );
          """);
          
          connUtil.execute("""
            DELETE FROM data_dtable_row WHERE owner_account_id IS NULL;
            DELETE FROM data_dtable_row WHERE dtable_id IS NULL;
            DELETE FROM data_dtable_permission WHERE user_id IS NULL;
            """);
          
          connUtil.execute("""
            ALTER TABLE data_dtable_column_group DROP COLUMN owner;
            ALTER TABLE data_dtable_column_group DROP COLUMN owner_employee_id;
            ALTER TABLE data_dtable_column_group DROP COLUMN owner_label;
            ALTER TABLE data_dtable_row DROP COLUMN owner;
            ALTER TABLE data_dtable_row DROP COLUMN owner_employee_id;
            ALTER TABLE data_dtable_row DROP COLUMN spread_sheet_code;
            ALTER TABLE data_dtable_row ALTER COLUMN dtable_id SET NOT NULL;
            ALTER TABLE data_dtable_permission DROP COLUMN login_id;
            ALTER TABLE okr_project_permission DROP COLUMN login_id;
            ALTER TABLE project_permission DROP COLUMN login_id;
            ALTER TABLE lgc_job_tracking_project_permission DROP COLUMN login_id;
            ALTER TABLE lgc_tms_partner_permission DROP COLUMN login_id;
            ALTER TABLE partner DROP COLUMN login_id;
          """);
        }
    };
    addRunnable(alterTables);
  }
}

ShellApplicationContext shellContext = (ShellApplicationContext) SHELL_CONTEXT;
Connection conn = shellContext.getPrimaryDBConnection();

DBConnectionUtil connUtil = new DBConnectionUtil(conn);

RunnableReporter reporter = new RunnableReporter("dbmigration", "latest")

AlterTableCommonSet migration = new AlterTableCommonSet();
migration.run(reporter, connUtil);

connUtil.close();
return "DONE!!!"