package net.datatp.module.company.hr.timesheet;

import static org.junit.Assert.assertNotNull;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.Getter;
import net.datatp.module.company.hr.timesheet.entity.Payslip;
import net.datatp.module.company.hr.timesheet.entity.PayslipAdjustment;
import net.datatp.module.company.hr.timesheet.entity.PayslipAdjustment.AdjustmentMethod;
import net.datatp.module.company.hr.timesheet.entity.PayslipAdjustment.AdjustmentStatus;
import net.datatp.module.company.hr.timesheet.entity.PayslipAdjustment.AdjustmentType;
import net.datatp.module.company.hr.timesheet.entity.PayslipDeduction;
import net.datatp.module.company.hr.timesheet.entity.PayslipPrepaid;
import net.datatp.module.company.hr.timesheet.entity.TimeTrackingDaily;
import net.datatp.module.company.hr.timesheet.model.BulkApprovalAdjustmentRequest;
import net.datatp.module.company.hr.timesheet.model.BulkCreatePayslipAdjustmentRequest;
import net.datatp.module.company.hr.timesheet.repository.PayslipAdjustmentRepository;
import net.datatp.module.company.hr.timesheet.repository.PayslipDeductionRepository;
import net.datatp.module.company.hr.timesheet.repository.PayslipPrepaidRepository;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.Join;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Component
public class PayslipAdjustmentLogic extends DAOService {
  
  @Autowired @Getter
  private PayslipLogic payslipLogic;
  
  @Autowired
  private EmployeeLogic employeeLogic;
  
  @Autowired @Getter
  private PayslipAdjustmentRepository adjustmentRepo;
  
  @Autowired @Getter
  private PayslipPrepaidRepository prepaidRepo;
  
  @Autowired
  private PayslipDeductionRepository deductionRepo;
  
  public PayslipAdjustment getPayslipAdjustment(ClientContext client, ICompany company, Long id) {
    return adjustmentRepo.getById(id);
  }
  
  public boolean changePayslipAdjustmentsStorageState(ClientContext client, ChangeStorageStateRequest req) {
    adjustmentRepo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
  
  public boolean bulkApprovalPayslipAdjustments(ClientContext client, ICompany company, BulkApprovalAdjustmentRequest req) {
    AdjustmentStatus status = req.getAdjustmentStatus();
    List<Long> adjustmentIds = req.getAdjustmentIds();
    if(Collections.isEmpty(adjustmentIds)) {
      return false;
    }
    
    for(Long id : adjustmentIds) {
      PayslipAdjustment adjustment = getPayslipAdjustment(client, company, id);
      Objects.assertNotNull(adjustment, "Not found PayslipAdjustment " + id);
      PayslipAdjustment cloneAdjustment = DataSerializer.JSON.clone(adjustment);
      cloneAdjustment.setAdjustmentStatus(status);
      savePayslipAdjustment(client, company, cloneAdjustment);
    }
    return true;
  }
  
  public boolean bulkCreatePayslipAdjustment(ClientContext client, ICompany company, BulkCreatePayslipAdjustmentRequest req) {
    if(Objects.isNull(req)) return false;
    float workingHour = req.getWorkingHours();
    float otHour = req.getOtHours();
    double hourUnitPay = req.getHourUnitPay();
    double totalPay = req.getTotalPay();
    Date date = req.getDate();
    String label = req.getLabel();
    Long approvedByAccountId = req.getApprovedByAccountId();
    String note = req.getNote();
    AdjustmentStatus status = req.getAdjustmentStatus();
    AdjustmentType type     = req.getAdjustmentType();
    AdjustmentMethod method = req.getAdjustmentMethod();
    boolean subjectToTax = req.isSubjectToTax();
    
    List<Long> payslipIds = req.getPayslipIds();
    if(Collections.isEmpty(payslipIds)) {
      return false;
    }
    
    for(Long payslipId : payslipIds) {
      Payslip payslip = payslipLogic.getPayslip(client, company, payslipId);
      Objects.assertNotNull(payslip, "Not found Payslip " + payslipId);
      
      PayslipAdjustment adj = new PayslipAdjustment(payslip);
      adj.setLabel(label);
      adj.setAdjustmentType(type);
      adj.setAdjustmentStatus(status);
      adj.setWorkingHours(workingHour);
      adj.setOtHours(otHour);
      adj.setTotalPay(totalPay);
      adj.setHourUnitPay(hourUnitPay);
      adj.setAdjustmentMethod(method);
      adj.setDate(date);
      adj.setNote(note);
      adj.setSubjectToTax(subjectToTax);
      
      if(Objects.nonNull(approvedByAccountId)) {
        Employee approvedByEmployee = employeeLogic.getByAccount(client, company, approvedByAccountId);
        Objects.assertNotNull(approvedByEmployee, "Approved By Employee {0} is not found!", approvedByAccountId);
        adj.setApprovedByEmployeeId(approvedByEmployee.getId());
        adj.setApprovedByLabel(approvedByEmployee.getLabel());
      }
      savePayslipAdjustment(client, company, adj);
    }
    return true;
  }

  public PayslipAdjustment savePayslipAdjustment(ClientContext client, ICompany company, PayslipAdjustment adj) {
    adj.set(client, company);
    adj = adj.genLabel();
    if(adj.getAdjustmentType() == AdjustmentType.OT_HOUR) {
      processOTHour(client, company, adj);
    }
    
    Long approvedByEmployeeId = adj.getApprovedByEmployeeId();
    if(Objects.nonNull(approvedByEmployeeId)) {
      Employee approvedByEmployee = employeeLogic.getEmployee(client, company, approvedByEmployeeId);
      Objects.assertNotNull(approvedByEmployee, "Approved By Employee {0} is not found!", approvedByEmployeeId);
      adj.setApprovedByEmployeeId(approvedByEmployee.getId());
      adj.setApprovedByLabel(approvedByEmployee.getLabel());
    }
    
    adj = adjustmentRepo.save(adj);
    if (adj.getPayslipId() != null) {
      payslipLogic.recomputePayslip(client, company, adj.getPayslipId());
    }
    return adj;
  }

  private void processOTHour(ClientContext client, ICompany company, PayslipAdjustment adj) {
    assertNotNull(adj.getDate());
    String dailyCode = adj.getEmployeeId() + "-" + DateUtil.asCompactDateId(adj.getDate());
    TimeTrackingDaily timesheetDaily = payslipLogic.getDailyLogic().getTimeTrackingDaily(client, company, dailyCode);
    if(Objects.isNull(timesheetDaily)) {
      Employee employee = employeeLogic.getEmployee(client, company, adj.getEmployeeId());
      timesheetDaily = new TimeTrackingDaily(employee).withTime(adj.getDate(), adj.getDate());
    }
    timesheetDaily.setOtHours(adj.getOtHours());
    payslipLogic.getDailyLogic().saveTimeTrackingDaily(client, company, timesheetDaily);
  }
  
  public List<PayslipAdjustment> findPayslipAdjustments(ICompany company, Long payslipId){
    return adjustmentRepo.findByPayslip(company.getId(), payslipId);
  }

  public List<PayslipAdjustment> findPayslipAdjustmentsByEmployee(ICompany company, Long employeeId){
    return adjustmentRepo.findByEmployee(company.getId(), employeeId);
  }
  
  public List<PayslipAdjustment> findApprovedPayslipAdjustments(ICompany company, Long payslipId){
    return adjustmentRepo.findApprovedAdjusmentByPayslip(company.getId(), payslipId);
  }
  
  public List<SqlMapRecord> searchPayslipAdjustments(ClientContext client, ICompany company, SqlQueryParams params){
    params.addParam("companyId", company.getId());
    SqlQuery query =
        new SqlQuery()
        .ADD_TABLE(new EntityTable(PayslipAdjustment.class).selectAllFields())
        .JOIN(new Join("LEFT JOIN", Payslip.class)
            .addSelectField("label", "payslipLabel")
            .addSelectField("period", "period")
            .ON("id", PayslipAdjustment.class, "payslipId"))
        .FILTER(ClauseFilter.company(PayslipAdjustment.class))
        .FILTER(new SearchFilter(PayslipAdjustment.class, new String[] {"label"}, "LIKE", "search"))
        .FILTER(
            new OptionFilter(PayslipAdjustment.class, "storageState", "=", StorageState.ALL).value(StorageState.ACTIVE),
            new RangeFilter(PayslipAdjustment.class, "createdTime"),
            new RangeFilter(PayslipAdjustment.class, "modifiedTime"))
        .ORDERBY(new String[] {"modifiedTime" }, "modifiedTime", "DESC");
    
    if(params.hasParam("payslipId")) {
      query.FILTER(new ClauseFilter(PayslipAdjustment.class, "payslipId", "=", ":payslipId"));
    }
    query.mergeValue(params);
    return query(client, query).getSqlMapRecords();
  }
  
  // Prepaid
  public PayslipPrepaid getPayslipPrepaid(ClientContext client, ICompany company, Long id) {
    return prepaidRepo.getById(id);
  }

  public List<PayslipPrepaid> findPayslipPrepaidByCompany(ClientContext client, ICompany company) {
    return prepaidRepo.findByCompanyId(company.getId());
  }
  
  public PayslipPrepaid savePayslipPrepaid(ClientContext client, ICompany company, PayslipPrepaid prepaid) {
    prepaid.set(client, company);
    if(StringUtil.isEmpty(prepaid.getPrepaidType())) {
      prepaid.setPrepaidType("Salary");
    }
    
    Long approvedByEmployeeId = prepaid.getApprovedByEmployeeId();
    if(Objects.nonNull(approvedByEmployeeId)) {
      Employee approvedByEmployee = employeeLogic.getEmployee(client, company, approvedByEmployeeId);
      Objects.assertNotNull(approvedByEmployee, "Approved By Employee {0} is not found!", approvedByEmployeeId);
      prepaid.setApprovedByEmployeeId(approvedByEmployee.getId());
      prepaid.setApprovedByLabel(approvedByEmployee.getLabel());
    }
    
    prepaid = prepaidRepo.save(prepaid);
    payslipLogic.recomputePayslip(client, company, prepaid.getPayslipId());
    return prepaid;
  }

  public boolean changePayslipPrepaidsStorageState(ClientContext client, ChangeStorageStateRequest req) {
    prepaidRepo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
  
  public List<PayslipPrepaid> findPrepaidsByEmployee(ICompany company, Long employeeId){
    return prepaidRepo.findPrepaidsByEmployee(company.getId(), employeeId);
  }
  
  public List<PayslipPrepaid> findPayslipPrepaidByPayslip(ICompany company, Long payslipId){
    return prepaidRepo.findPrepaidsByPayslip(company.getId(), payslipId);
  }
  
  public List<PayslipPrepaid> findApprovedPayslipPrepaidByPayslip(ICompany company, Long payslipId){
    return prepaidRepo.findApprovedPrepaidsByPayslip(company.getId(), payslipId);
  }
  
  public List<SqlMapRecord> searchPayslipPrepaids(ClientContext client, ICompany company, SqlQueryParams params){
    params.addParam("companyId", company.getId());
    SqlQuery query =
        new SqlQuery()
        .ADD_TABLE(new EntityTable(PayslipPrepaid.class).selectAllFields())
        .JOIN(new Join("LEFT JOIN", Payslip.class)
            .addSelectField("label", "payslipLabel")
            .addSelectField("period", "period")
            .ON("id", PayslipPrepaid.class, "payslipId"))
        .FILTER(ClauseFilter.company(PayslipPrepaid.class))
        .FILTER(new SearchFilter(PayslipPrepaid.class, new String[] {"label"}, "LIKE", "search"))
        .FILTER(
            new OptionFilter(PayslipPrepaid.class, "storageState", "=", StorageState.ALL).value(StorageState.ACTIVE),
            new RangeFilter(PayslipPrepaid.class, "createdTime"),
            new RangeFilter(PayslipPrepaid.class, "modifiedTime"))
        .ORDERBY(new String[] {"modifiedTime" }, "modifiedTime", "DESC");
    
    if(params.hasParam("payslipId")) {
      query.FILTER(new ClauseFilter(PayslipPrepaid.class, "payslipId", "=", ":payslipId"));
    }
    query.mergeValue(params);
    return query(client, query).getSqlMapRecords();
  }
  
  // Deduction
  public PayslipDeduction getPayslipDeduction(ClientContext client, ICompany company, Long id) {
    return deductionRepo.getById(id);
  }

  public List<PayslipDeduction> findPayslipDeductionByCompany(ClientContext client, ICompany company) {
    return deductionRepo.findByCompanyId(company.getId());
  }
  
  public PayslipDeduction savePayslipDeduction(ClientContext client, ICompany company, PayslipDeduction deduction) {
    deduction.set(client, company);
    deduction = deductionRepo.save(deduction);
    payslipLogic.recomputePayslip(client, company, deduction.getPayslipId());
    return deduction;
  }

  public boolean changePayslipDeductionsStorageState(ClientContext client, ChangeStorageStateRequest req) {
    deductionRepo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
  
  public List<PayslipDeduction> findPayslipDeductionByPayslip(ICompany company, Long payslipId){
    return deductionRepo.findPayslipDeductionsByPayslip(company.getId(), payslipId);
  }
  
  public List<PayslipDeduction> findPayslipDeductionsByEmployee(ICompany company, Long employeeId){
    return deductionRepo.findPayslipDeductionsByEmployee(company.getId(), employeeId);
  }
  
  public List<PayslipDeduction> findActivePayslipDeductionByPayslip(ICompany company, Long payslipId){
    return deductionRepo.findActivePayslipDeductionsByPayslip(company.getId(), payslipId);
  }
  
  public List<SqlMapRecord> searchPayslipDeductions(ClientContext client, ICompany company, SqlQueryParams params){
    params.addParam("companyId", company.getId());
    SqlQuery query =
        new SqlQuery()
        .ADD_TABLE(new EntityTable(PayslipDeduction.class).selectAllFields())
        .JOIN(new Join("LEFT JOIN", Payslip.class)
            .addSelectField("label", "payslipLabel")
            .addSelectField("period", "period")
            .ON("id", PayslipDeduction.class, "payslipId"))
        .FILTER(ClauseFilter.company(PayslipDeduction.class))
        .FILTER(new SearchFilter(PayslipDeduction.class, new String[] {"label"}, "LIKE", "search"))
        .FILTER(
            new OptionFilter(PayslipDeduction.class, "storageState", "=", StorageState.ALL).value(StorageState.ACTIVE),
            new RangeFilter(PayslipDeduction.class, "createdTime"),
            new RangeFilter(PayslipDeduction.class, "modifiedTime"))
        .ORDERBY(new String[] {"modifiedTime" }, "modifiedTime", "DESC");
    
    if(params.hasParam("payslipId")) {
      query.FILTER(new ClauseFilter(PayslipDeduction.class, "payslipId", "=", ":payslipId"));
    }
    query.mergeValue(params);
    return query(client, query).getSqlMapRecords();
  }

  public List<PayslipAdjustment> findPayslipAdjustmentByCompany(ClientContext client, ICompany company) {
    return adjustmentRepo.findByCompanyId(company.getId());
  }
}