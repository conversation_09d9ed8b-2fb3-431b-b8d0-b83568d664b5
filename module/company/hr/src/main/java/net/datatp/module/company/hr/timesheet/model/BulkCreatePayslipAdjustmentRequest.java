package net.datatp.module.company.hr.timesheet.model;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.hr.timesheet.entity.PayslipAdjustment.AdjustmentMethod;
import net.datatp.module.company.hr.timesheet.entity.PayslipAdjustment.AdjustmentStatus;
import net.datatp.module.company.hr.timesheet.entity.PayslipAdjustment.AdjustmentType;
import net.datatp.util.text.DateUtil;

@NoArgsConstructor @Getter @Setter
public class BulkCreatePayslipAdjustmentRequest {
  private String label;
  
  @Enumerated(EnumType.STRING)
  private AdjustmentType   adjustmentType;
  
  @Enumerated(EnumType.STRING)
  AdjustmentMethod         adjustmentMethod;
  
  @Enumerated(EnumType.STRING)
  private AdjustmentStatus adjustmentStatus;
  
  private float  workingHours;
  private float  otHours;
  private double hourUnitPay;
  private double totalPay;
  private boolean subjectToTax = true;
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date   date;
  private Long approvedByAccountId;
  private String note;

  private List<Long> payslipIds;
}
