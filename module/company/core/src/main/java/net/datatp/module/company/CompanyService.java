package net.datatp.module.company;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.Getter;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.company.entity.CompanyInfo;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;

@Service("CompanyService")
public class CompanyService {
  @Getter
  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private CompanyConfigLogic configLogic;

  @Transactional(readOnly=true)
  public Company loadCompanyById(ClientContext clientCtx, Long id) {
    return companyLogic.getCompany(clientCtx, id);
  }

  @Transactional(readOnly=true)
  public Company getCompany(ClientContext clientCtx, String companyCode) {
    return companyLogic.getCompany(clientCtx, companyCode);
  }

  @Transactional
  public Company createCompany(ClientContext clientCtx, Company company) {
    return companyLogic.createCompany(clientCtx, company);
  }

  @Transactional
  public Company saveCompany(ClientContext clientCtx, Company company) {
    return companyLogic.saveCompany(clientCtx, company);
  }

  @Transactional
  public List<Company> searchCompanies(ClientContext client, ICompany company, SqlQueryParams params) { 
    return companyLogic.searchCompanies(client, params);
  }

  @Transactional(readOnly = true)
  public CompanyConfig getCompanyConfigByCompanyId(ClientContext clientCtx, Long companyId) {
    return configLogic.getCompanyConfigByCompanyId(clientCtx, companyId);
  }

  @Transactional(readOnly = true)
  public Map<String, Object> getCompanyConfigAttrs(ClientContext clientCtx, ICompany company) {
    CompanyConfig config = configLogic.getCompanyConfigByCompanyId(clientCtx, company.getId());
    return config.toAttributeMap();
  }

  @Transactional
  public CompanyConfig saveCompanyConfig(ClientContext clientCtx, ICompany company, CompanyConfig companyConfig) {
    return configLogic.saveCompanyConfig(clientCtx, company, companyConfig);
  }

  //Company Info
  public CompanyInfo loadCompanyInfoById(ClientContext clientCtx, Long id) {
    return companyLogic.loadCompanyInfoById(clientCtx, id);
  }
  
  @Transactional(readOnly = true)
  public CompanyInfo getCompanyInfo(ClientContext clientCtx, String code) {
    return companyLogic.getCompanyInfo(clientCtx, code);
  }

  @Transactional
  public CompanyInfo saveCompanyInfo(ClientContext clientCtx, CompanyInfo companyInfo) {
    return companyLogic.saveCompanyInfo(clientCtx, companyInfo);
  }

}
