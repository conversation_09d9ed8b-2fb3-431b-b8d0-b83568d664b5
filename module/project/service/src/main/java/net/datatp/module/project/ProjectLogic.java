package net.datatp.module.project;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.security.client.ClientContext;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.partner.PartnerLogic;
import net.datatp.module.partner.entity.Partner;
import net.datatp.module.project.entity.Project;
import net.datatp.module.project.entity.ProjectPermission;
import net.datatp.module.project.entity.ProjectSpace;
import net.datatp.module.project.entity.TaskListConfig;
import net.datatp.module.project.plugin.ProjectPlugin;
import net.datatp.module.project.repository.ProjectPermissionRepository;
import net.datatp.module.project.repository.ProjectRepository;
import net.datatp.module.project.task.TaskLogic;
import net.datatp.module.project.task.entity.Task;
import net.datatp.module.project.task.repository.TaskRepository;
import net.datatp.module.storage.CompanyStorage;
import net.datatp.module.storage.IStorageService;
import net.datatp.util.ds.Objects;

@Component
public class ProjectLogic extends DAOService {
  @Autowired
  private ProjectRepository projectRepo;

  @Autowired @Getter
  private ProjectPermissionRepository permissionRepository;

  @Autowired
  private TaskRepository    taskRepo;

  @Autowired @Getter
  private TaskLogic taskLogic;
  
  @Autowired
  private AccountLogic accountLogic;
  
  @Autowired
  private EmployeeLogic employeeLogic;
  
  @Autowired
  private PartnerLogic partnerLogic;

  @Autowired
  private CompanyLogic companyLogic;
  
  @Autowired
  private IStorageService storageService;

  private Map<String, ProjectPlugin> plugins = new HashMap<>();

  @Autowired(required = false)
  public void setPlugins(List<ProjectPlugin> plugins) {
    for(ProjectPlugin plugin : plugins) {
      this.plugins.put(plugin.getType(), plugin);
    }
  }

  //Project
  public Project newProject(ClientContext client, ICompany company, Project project) {
    ProjectPlugin plugin = plugins.get(project.getPluginType());
    plugin.initProject(client, company, project);
    return project;
  }

  public Project getProject(ClientContext client, ICompany company, String code) {
    Project project = projectRepo.getByCode(company.getId(), code);
    if(project == null) return null;
    updateTaskListIndex(client, company, project);
    return project;
  }
  
  //@Cacheable(value = CachingConfig.REGION_ENTITY , key= "{'" + Project.TABLE_NAME + "', #id}")
  public Project getProject(ClientContext client, ICompany company, Long id) {
    return projectRepo.findById(id).get();
  }
  
  private List<Task> updateTaskListIndex(ClientContext client, ICompany company, Project project){
    List<Task> taskListInProject = 
        taskRepo.findTaskInProject(company.getId(), project.getId(), project.getProjectSpaceId());

    if (taskListInProject.stream().noneMatch(t -> t.getTaskListIndex() != 0)) {
      List<TaskListConfig> taskListConfigs = project.getTaskListConfigs();
      for (TaskListConfig taskListConfig : taskListConfigs) {
        List<Task> taskList = 
            taskRepo.findTaskInList(company.getId(), project.getId(), project.getProjectSpaceId(), taskListConfig.getName());
        int taskListIndex = 0;
        for (Task taskSel : taskList) {
          taskRepo.setTaskListIndex(taskListIndex, taskSel.getCode());
          taskListIndex++;
        }
      }
    }
    return taskListInProject;
  }

  public List<ProjectPermission> findProjectPermission(ClientContext client, Long companyId, Long userId) {
    return permissionRepository.findByUserId(companyId, userId);
  }

  public List<Project> findProjectByCompany(ClientContext client, ICompany company) {
    return projectRepo.findByCompany(company.getId());
  }

  public boolean changeProjectStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    projectRepo.updateProjectsStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }


  public String getProjectStoragePath(Project w) { return "apps/workflows/" + w.getCode(); }

  //@CacheEvict(value = CachingConfig.REGION_ENTITY , key= "{'" + Project.TABLE_NAME + "', #project.id}")
  public Project saveProject(ClientContext client, ICompany company, Project project) {
    ProjectPlugin plugin = plugins.get(project.getPluginType());
    Objects.assertNotNull(plugin, "Project plugin must be not null!, company = {} , project code = {}", company.getCode(), project.getCode());
    project.updateStepOrders();
    project.set(client, company);
    plugin.onPreSave(client, company, project);
    project = projectRepo.save(project);
    
    if(project.isNew()) {
      String wflStoragePath = getProjectStoragePath(project) ;
      CompanyStorage storage = storageService.createCompanyStorage(client, company.getCode());
      storage.initDirectory(wflStoragePath);
    }
    plugin.onPostSave(client, company, project);
    return project;
  }
  

  public List<SqlMapRecord> searchProjects(ClientContext client, ICompany company, SqlQueryParams params) {
    params.addParam("companyId", company.getId());
    SqlQuery query =
        new SqlQuery()
        .ADD_TABLE(new EntityTable(Project.class).selectAllFields())
        .ADD_TABLE(
            new EntityTable(ProjectSpace.class)
            .addSelectField("code", "spaceCode")
            .addSelectField("label", "spaceLabel")
            )
        .FILTER(
             ClauseFilter.company(Project.class),
            new ClauseFilter(Project.class, "projectSpaceId", "=", ProjectSpace.class, "id"))
        .FILTER(
             SearchFilter.isearch(Project.class, "code", "label"))
        .FILTER(
             OptionFilter.storageState(Project.class),
             RangeFilter.createdTime(Project.class),
             RangeFilter.modifiedTime(Project.class))
        .ORDERBY(new String[] { "modifiedTime" }, "modifiedTime", "ASC");

    Company targetCompany = companyLogic.getCompany(client, company.getId());
    if("admin".equals(client.getRemoteUser()) || targetCompany.getAdminAccountId().equals(client.getAccountId())) {
      
    } else {
      query
        .ADD_TABLE(new EntityTable(ProjectPermission.class))
        .FILTER(
          new ClauseFilter(Project.class, "id", "=", ProjectPermission.class, "projectId"),
          new ClauseFilter(ProjectPermission.class, "userId", "=", ":userId"));
      Account account = accountLogic.getEditable(client, client.getRemoteUser());
      params.addParam("userId", account.getId());
    }

    List<SqlMapRecord> recordList = query(client, query, params).getSqlMapRecords();
    if(recordList.size() == 0) return recordList;

    List<Long> projectIds = fieldValueOf(recordList, "id", Long.class);
    boolean myTaskOnly = params.hasParam("myTaskOnly");
    HashMap<Long, SqlMapRecord> taskSummaryMap = findTaskSummaryByProjectIds(client, company, projectIds, myTaskOnly);

    for(SqlMapRecord record : recordList) {
      Long projectId = (Long) record.get("id");
      SqlMapRecord taskSummary = taskSummaryMap.get(projectId);
      record.put("taskSummary", taskSummary);
    }
    return recordList;
  }

  private HashMap<Long, SqlMapRecord> findTaskSummaryByProjectIds(
      ClientContext client, ICompany company, List<Long> projectIds, boolean myTaskOnly) {
    SqlQuery taskQuery =
        new SqlQuery().
        ADD_TABLE(
            new EntityTable(Task.class)
            .addSelectField("label", "taskLabel")
            .addSelectField("projectId", "projectId")
            .addSelectField("taskListName", "taskListName"))
        .FILTER(new ClauseFilter(Task.class, "projectId", "IN", "(:projectIds)"))
        .FILTER(OptionFilter.storageState(Task.class));
    if(myTaskOnly) {
      taskQuery
      .FILTER(
          new ClauseFilter(Task.class, "creatorEmployeeId", "=", ":myTaskEmployeeId")
          .OR(Task.class, "clientPartnerId", "=", ":myTaskPartnerId")
          .OR(Task.class, "assigneeEmployeeId", "=", ":myTaskEmployeeId")
          .OR(Task.class, "reporterEmployeeId", "=", ":myTaskEmployeeId"));
 
    }
    Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    if (employee != null) taskQuery.addParam("myTaskEmployeeId", employee.getId());
    
    Partner partner = partnerLogic.getPartnerByLoginIdPattern(client, company, client.getRemoteUser());
    if (partner != null) taskQuery.addParam("myTaskPartnerId", partner.getId());
    
    taskQuery.addParam("projectIds", projectIds);

    List<SqlMapRecord> taskRecords = query(client, taskQuery).getSqlMapRecords();
    RecordGroupByMap<Long, SqlMapRecord> taskMap = new RecordGroupByMap<>(taskRecords, ref -> (Long)ref.get("projectId"));

    HashMap<Long, SqlMapRecord> taskSummaryMap = new HashMap<>();
    for(Map.Entry<Long, List<SqlMapRecord>> entry : taskMap.getAll().entrySet()) {
      Long projectId = entry.getKey();
      List<SqlMapRecord> projTasks = entry.getValue();
      SqlMapRecord taskSummary = new SqlMapRecord();
      taskSummary.put("projectId", projectId);
      taskSummary.put("taskCount", projTasks.size());
      for(SqlMapRecord task : projTasks) {
        String stepLabel = task.getString("taskListName");
        Long count = taskSummary.getLong(stepLabel);
        if(count == null) taskSummary.put(stepLabel, 1l);
        else taskSummary.put(stepLabel, count + 1);
      }
      taskSummaryMap.put(projectId, taskSummary);
    }
    return taskSummaryMap;
  }

  public Project deleteProjectTaskList(ClientContext client, ICompany company, Project project, String taskListName) {
    List<TaskListConfig> taskListConfig = project.getTaskListConfigs();
    List<Task> taskInDeleteList = taskRepo.findByTaskListName(taskListName);
    for(Task sel : taskInDeleteList) {
      taskRepo.setTaskState(StorageState.ARCHIVED, sel.getCode());
      taskLogic.archivedChildTasks(client, company, sel);
    }
    Iterator<TaskListConfig> taskListConfigIterator = taskListConfig.iterator();
    while (taskListConfigIterator.hasNext()) {
      TaskListConfig selectTaskListConfig = taskListConfigIterator.next();
      if(selectTaskListConfig.getName().equals(taskListName)) {
        taskListConfigIterator.remove();
        break;
      }
    }
    project.setTaskListConfigs(taskListConfig);
    project.updateStepOrders();
    return saveProject(client, company, project);
  }
}