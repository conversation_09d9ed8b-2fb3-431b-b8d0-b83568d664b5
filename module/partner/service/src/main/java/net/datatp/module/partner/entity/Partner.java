package net.datatp.module.partner.entity;

import java.io.Serial;
import java.util.HashSet;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.AccountType;
import net.datatp.module.company.entity.ShareableCompanyEntity;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.StringUtil;

/**
 * <AUTHOR>
 */
@Entity
@Table(
  name = Partner.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = Partner.TABLE_NAME + "_account_id",
      columnNames = { "company_id", "account_id"}
    ),
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class Partner extends ShareableCompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  final static public String TABLE_NAME = "partner";

  @Column(name="name", length = 1024)
  private String name;

  @Column(name = "label", length = 1024)
  private String label;

  @Column(name="legacy_company_login_id")
  private String companyLegacyLoginId;

  @NotNull
  @Column(name="account_id")
  private Long   accountId;

  @Column(name="move_partner_id")
  private Long  movePartnerId;

  @Enumerated(EnumType.STRING)
  @Column(name="partner_account_type")
  private AccountType partnerAccountType;

  @Column(name="org_partner_id")
  private Long orgPartnerId;

  @Column(name="org_partner_label", length = 1024)
  private String orgPartnerLabel;

  @Column(name="parent_id")
  private Long parentId;

  @Column(name="odoo_14_partner_id")
  private Long odoo14PartnerId;

  @Column(name="odoo_16_partner_id")
  private Long odoo16PartnerId;

  @Column(name="odoo_partner_ref")
  private String odooPartnerRef;

  @Column(name="parent_label", length = 1024)
  private String parentLabel;

  @Column(name="company_priority")
  private int companyPriority;

  @Column(length = 64 * 1024)
  private String description;

  @JsonProperty("partnerTypes")
  @ManyToMany(cascade = {CascadeType.MERGE})
  @JoinTable(
    name = "partner_type_rel",
    joinColumns = {
      @JoinColumn(name = "partner_id", referencedColumnName = "id"),
      @JoinColumn(name = "company_id", referencedColumnName = "company_id", nullable = false),
    },
    inverseJoinColumns = @JoinColumn(name = "partner_type_id")
  )
  private Set<PartnerType> partnerTypes = new HashSet<>();

  @JsonProperty("partnerTags")
  @ManyToMany(cascade = {CascadeType.MERGE})
  @JoinTable(
    name = "partner_tag_rel",
    joinColumns = {
      @JoinColumn(name = "partner_id", referencedColumnName = "id"),
      @JoinColumn(name = "company_id", referencedColumnName = "company_id"),
    },
    inverseJoinColumns = @JoinColumn(name = "partner_tag_id")
  )
  private Set<PartnerTag> partnerTags = new HashSet<>();

  public Partner(String label) {
    this.label = label;
  }

  public Partner withLabel(String label) {
    setLabel(label);
    return this;
  }

  public Partner withDescription(String des) {
    setDescription(des);
    return this;
  }

  public Partner withTag(PartnerTag ... tags) {
    Arrays.addToSet(partnerTags, tags);
    return this;
  }

  public Partner withPartnerType(PartnerType type) {
    Arrays.addToSet(partnerTypes, type);
    return this;
  }

  public Partner removePartnerType(PartnerType type) {
    if (partnerTypes != null) {
      partnerTypes.remove(type);
    }
    return this;
  }

  public Partner withPrivateShareableScope() {
    setShareable(ShareableScope.PRIVATE);
    return this;
  }

  static public boolean isTheSame(Partner p1, Partner p2) {
    if(p1 == null || p2 == null) return false;
    return p1.getName().equals(p2.getName());
  }

  public Partner merge(Partner sel) {
    if(StringUtil.isEmpty(label)) label = sel.getLabel();
    if(StringUtil.isEmpty(description)) description = sel.getDescription();
    sel.getPartnerTypes().forEach(this::withPartnerType);
    sel.getPartnerTags().forEach(this::withTag);
    return this;
  }
  
  public Partner mergeWithBFSOneMapRecord(MapObject bfsOnePartnerData) {
    String legacyLoginId = bfsOnePartnerData.getString("partnerId");
    String partnerName   = bfsOnePartnerData.getString("partnerName");
    String partnerName3  = bfsOnePartnerData.getString("partnerName3");
    
    name                 = partnerName;
    label                = partnerName3;
    companyLegacyLoginId = legacyLoginId;
    shareable            = ShareableScope.PRIVATE;
    return this;
  }

  public String identify() { return this.name; }
}