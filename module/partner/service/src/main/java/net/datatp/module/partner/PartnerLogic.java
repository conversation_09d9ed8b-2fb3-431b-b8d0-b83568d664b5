package net.datatp.module.partner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.LoginIdAnalyzer;
import net.datatp.module.account.NewAccountModel;
import net.datatp.module.account.ProfileLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.account.entity.AccountType;
import net.datatp.module.account.entity.OrgProfile;
import net.datatp.module.account.entity.UserProfile;
import net.datatp.security.client.ClientContext;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.security.client.AllowAccessCompany;
import net.datatp.security.client.AccessType;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.partner.entity.Partner;
import net.datatp.module.partner.entity.PartnerGroup;
import net.datatp.module.partner.entity.PartnerGroupRelation;
import net.datatp.module.partner.entity.PartnerType;
import net.datatp.module.partner.plugin.PartnerServicePlugin;
import net.datatp.module.partner.plugin.PartnerTypePlugin;
import net.datatp.module.partner.repository.PartnerGroupRelationRepository;
import net.datatp.module.partner.repository.PartnerGroupRepository;
import net.datatp.module.partner.repository.PartnerRepository;
import net.datatp.module.partner.repository.PartnerTypeRepository;
import net.datatp.module.resource.entity.OwnerType;
import net.datatp.module.resource.location.CountryLogic;
import net.datatp.module.resource.location.entity.Country;
import net.datatp.module.resource.misc.ContactLogic;
import net.datatp.module.resource.misc.entity.AccountContact;
import net.datatp.module.resource.misc.entity.ContactType;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;

@Component
public class PartnerLogic extends DAOService {
  @Autowired
  @Getter
  private AccountLogic accountLogic;

  @Autowired
  @Getter
  private ProfileLogic profileLogic;

  @Autowired
  @Getter
  private ContactLogic contactLogic;

  @Autowired
  @Getter
  private CountryLogic countryLogic;

  @Autowired
  @Getter
  private CompanyLogic companyLogic;

  
  @Autowired
  @Getter
  private PartnerRepository partnerRepo;

  @Autowired
  private PartnerTypeRepository repo;

  @Autowired
  private PartnerGroupRelationRepository relationRepo;

  @Autowired
  private PartnerGroupRepository groupRepo;

  @Autowired
  private LoginIdAnalyzer loginIdAnalyzer;

  @Autowired
  protected ApplicationContext context;

  @Autowired(required = false)
  List<PartnerServicePlugin> plugins = new ArrayList<>();

  Map<String, PartnerTypePlugin> partnerTypePlugins = new HashMap<>();

  @Autowired(required = false)
  public void setPartnerTypePlugins(List<PartnerTypePlugin> plugins) {
    for (PartnerTypePlugin sel : plugins) {
      partnerTypePlugins.put(sel.getType(), sel);
    }
  }

  public Partner getByAccount(ClientContext client, ICompany company, Long id) {
    return partnerRepo.getByAccountId(company.getId(), id);
  }

  public Partner getById(ClientContext client, ICompany company, Long id) {
    return partnerRepo.getById(id);
  }

  public Partner getPartnerByLoginId(ClientContext client, ICompany company, String loginId) {
    return partnerRepo.getByLoginId(company.getId(), loginId);
  }

  public Partner getPartnerByLoginIdPattern(ClientContext client, ICompany company, String loginId) {
    if (loginIdAnalyzer.isLegacyLoginId(loginId)) {
      return partnerRepo.getByLegacyLoginId(company.getId(), loginId);
    } else {
      return partnerRepo.getByLoginId(company.getId(), loginId);
    }
  }

  public List<Partner> findByLegacyLoginId(ClientContext client, ICompany company, String loginId) {
    return partnerRepo.findByLegacyLoginId(company.getId(), loginId);
  }

  public Partner getCompanyPartner(ClientContext client, ICompany company) {
    Company targetCompany = companyLogic.getCompany(client, company.getId());
    final Account companyAcc = accountLogic.getAccountById(client, targetCompany.getAdminAccountId());
    return Optional.ofNullable(getByAccount(client, targetCompany, companyAcc.getId()))
        .orElseThrow(
            () -> new IllegalArgumentException("Company Partner must be not null, code = " + company.getCode()));
  }

  public List<Partner> findPartners(ClientContext client, ICompany company, List<Long> ids) {
    return partnerRepo.findPartners(ids);
  }

  public List<Partner> findPartnersByName(ClientContext client, ICompany company, String name) {
    Company targetCompany = companyLogic.getCompany(client, company.getId());
    final List<Long> companyIds = targetCompany.findCompanyIdPaths();
    return partnerRepo.findPartnersByName(company.getId(), companyIds, client.getRemoteUser(), name);
  }

  public Partner getPartnerByOdooRef(ClientContext client, ICompany company, String odooRef) {
    return partnerRepo.getByOdooRef(company.getId(), odooRef);
  }

  public List<Partner> findPartners(ClientContext client, ICompany company) {
    return partnerRepo.findPartnersByCompanyId(company.getId());
  }

  public Partner savePartner(ClientContext client, ICompany company, Partner partner) {
    Objects.assertNotNull(partner.getAccountId(), "Partner Account {0} is not found", partner.getName());
    partner.set(client, company);
    return partnerRepo.save(partner);
  }

  public Partner createPartner(ClientContext client, ICompany company, NewPartnerModel model) {
    Account account = model.getAccount();
    if (account.isNew()) {
      accountLogic.createNewAccount(client, model);
      account = accountLogic.getEditable(client, account.getLoginId());
    }

    Partner partner = model.getPartner();
    if (partner == null) {
      partner = new Partner();
      partner.setPartnerAccountType(account.getAccountType());
    }
    partner.setAccountId(account.getId());
    partner = savePartner(client, company, partner);

    if (Objects.nonNull(model.getPartnerGroupIds())) {
      for (Long groupPathId : model.getPartnerGroupIds()) {
        PartnerGroup group = groupRepo.findById(groupPathId).get();
        PartnerGroupRelation relation = new PartnerGroupRelation(partner, group);
        relation.set(client, company);
        relationRepo.save(relation);
      }
    }
    return partner;
  }

  public PartnerMergeBFSOnePartnerPreviewModel processingMergeBFSOnePartner(
      ClientContext client, ICompany company, PartnerMergeBFSOnePartnerPreviewModel model) {
    Partner partner = model.getPartner();
    partner = savePartner(client, company, partner);

    Account account = model.getAccount();
    OrgProfile profile = profileLogic.saveOrgProfile(client, model.getOrgProfile());

    List<AccountContact> contacts = model.getContacts();
    contacts = contactLogic.saveContacts(client, OwnerType.Account, account.getId(), contacts);

    model.setPartner(partner);
    model.setOrgProfile(profile);
    model.setContacts(contacts);
    return model;
  }

  public PartnerMergeBFSOnePartnerPreviewModel previewMergePartnerWithBFSOnePartnerData(
      ClientContext client, ICompany company, Partner partner, MapObject bfsOnePartnerData) {
    PartnerMergeBFSOnePartnerPreviewModel results = new PartnerMergeBFSOnePartnerPreviewModel();

    partner.mergeWithBFSOneMapRecord(bfsOnePartnerData);
    results.setPartner(partner);

    Account account = accountLogic.getAccountById(client, partner.getAccountId());
    account.mergeWithBFSOneMapRecord(bfsOnePartnerData);
    results.setAccount(account);

    String countryName = bfsOnePartnerData.getString("country");
    String address = bfsOnePartnerData.getString("address2", "");
    boolean addressExists = false;
    List<AccountContact> contacts = contactLogic.findContactByOwnerId(client, OwnerType.Account, account.getId());
    for (AccountContact contact : contacts) {
      if (address.equals(contact.getAddress()) && !addressExists) {
        contact.setType(ContactType.Primary);
        addressExists = true;
      }
      contact.setType(ContactType.Secondary);
    }
    if (!addressExists) {
      AccountContact contact = new AccountContact("Contact");
      List<Country> countries = countryLogic.findCountriesByPattern(client, countryName);
      if (Collections.isNotEmpty(countries)) {
        Country country = countries.get(0);
        contact.setCountryName(country.getCode());
        contact.setCountryLabel(country.getLabel());
      }
      contact.setOwnerId(account.getId());
      contact.setOwnerIdentifier(account.getLoginId());
      contact.mergeWithBFSOneMapRecord(bfsOnePartnerData);

      if (contacts == null)
        contacts = new ArrayList<>();
      contacts.add(contact);
    }
    results.setContacts(contacts);

    OrgProfile profile = profileLogic.getOrgProfile(client, account.getLoginId());
    profile.mergeWithBFSOneMapRecord(bfsOnePartnerData);

    results.setOrgProfile(profile);
    return results;
  }

  public Partner createPartnerWithBFSOnePartnerData(ClientContext client, ICompany company, MapObject bfsOnePartnerData) {
    String legacyLoginId = bfsOnePartnerData.getString("partnerId");
    String countryName = bfsOnePartnerData.getString("country");
    String groupName = bfsOnePartnerData.getString("groupName");

    List<Partner> partners = findByLegacyLoginId(client, company, legacyLoginId);
    if (Collections.isNotEmpty(partners))
      return null;
    NewPartnerModel partnerModel = new NewPartnerModel();

    Account account = accountLogic.getEditable(client, legacyLoginId);
    if (account == null) {
      account = new Account();
      account.setLoginId(legacyLoginId);
      account.mergeWithBFSOneMapRecord(bfsOnePartnerData);

      NewAccountModel accountModel = new NewAccountModel(account);
      accountModel.getOrgProfile().mergeWithBFSOneMapRecord(bfsOnePartnerData);
      accountModel = accountLogic.createNewAccount(client, new NewAccountModel(account));

      AccountContact contact = new AccountContact("Contact");
      List<Country> countries = countryLogic.findCountriesByPattern(client, countryName);
      if (Collections.isNotEmpty(countries)) {
        Country country = countries.get(0);
        contact.setCountryName(country.getCode());
        contact.setCountryLabel(country.getLabel());
      }
      contact.setOwnerId(account.getId());
      contact.setOwnerIdentifier(account.getLoginId());
      contact.mergeWithBFSOneMapRecord(bfsOnePartnerData);
      contactLogic.saveContact(client, contact);

      partnerModel.setOrgProfile(accountModel.getOrgProfile());
      partnerModel.setAccount(accountModel.getAccount());
    } else {
      partnerModel.withAccount(account);
    }

    Partner partner = new Partner();
    partner.setAccountId(account.getId());
    partner.mergeWithBFSOneMapRecord(bfsOnePartnerData);
    partnerModel.setPartner(partner);

    if (StringUtil.isNotBlank(groupName)) {
      PartnerGroup partnerGroup = groupRepo.getByName(company.getId(), groupName);
      if (partnerGroup != null)
        partnerModel.setPartnerGroupIds(Arrays.asList(partnerGroup.getId()));
    }

    return createPartner(client, company, partnerModel);
  }

  public ClonePartnerModel newClonePartner(ClientContext client, ICompany company, Partner partner) {
    Account account = accountLogic.getAccountById(client, partner.getAccountId());
    Objects.assertNotNull(account, "Account is not found!, name = {}", partner.getName());
    ClonePartnerModel model = new ClonePartnerModel();
    model.setLoginId(account.getLoginId());
    model.setLabel(partner.getLabel());
    model.setName(partner.getName());
    model.setAccountType(partner.getPartnerAccountType());
    model.setOrgPartnerLabel(partner.getOrgPartnerLabel());
    model.setOrgPartnerId(partner.getOrgPartnerId());
    model.setParentLabel(partner.getParentLabel());
    model.setParentId(partner.getParentId());
    model.setDescription(partner.getDescription());
    model.setPartnerTags(partner.getPartnerTags());

    List<AccountContact> contacts = contactLogic.findContactByOwnerId(client, OwnerType.Account, account.getId());
    for (AccountContact ac : contacts) {
      AccountContact accountContact = DataSerializer.jsonClone(ac);
      accountContact.clearIds();
      accountContact.setOwnerId(null);
      accountContact.setOwnerIdentifier(null);
      model.getContacts().add(accountContact);
    }
    return model;
  }

  public Partner createClonePartner(ClientContext client, ICompany company, ClonePartnerModel model) {
    String loginId = model.getLoginId();
    Account account = accountLogic.getEditable(client, loginId);
    Objects.assertNull(account, "Account {0} is existed", loginId);
    account = new Account(loginId, model.getPassword(), "", "", model.getLabel(), model.getAccountType());
    accountLogic.createNewAccount(client, new NewAccountModel(account));
    List<AccountContact> contacts = model.getContacts();
    for (AccountContact sel : contacts) {
      sel.setOwnerType(OwnerType.Account);
      sel.setOwnerId(account.getId());
      sel.setOwnerIdentifier(account.getLoginId());
    }
    contactLogic.saveContacts(client, OwnerType.Account, account.getId(), contacts);

    Partner existPartner = getPartnerByLoginIdPattern(client, company, loginId);
    Objects.assertNull(existPartner, "Partner {0} is existed", loginId);
    Partner partner = new Partner(model.getLabel());
    partner.setName(model.getName());
    partner.setPartnerAccountType(model.getAccountType());
    partner.setOrgPartnerLabel(model.getOrgPartnerLabel());
    partner.setOrgPartnerId(model.getOrgPartnerId());
    partner.setParentLabel(model.getParentLabel());
    partner.setParentId(model.getParentId());
    partner.setDescription(model.getDescription());
    partner.setShareable(model.getShareable());
    partner.setPartnerTags(model.getPartnerTags());
    partner = savePartner(client, company, partner);
    return partner;
  }

  public Partner mergePartner(ClientContext client, ICompany company, String srcLoginId, List<String> srcLoginIds) {
    // EmployeeLogic employeeLogic = context.getBean(EmployeeLogic.class);
    Collections.assertNotEmpty(srcLoginIds);
    accountLogic.mergeAccount(client, srcLoginId, srcLoginIds);
    Partner srcPartner = getPartnerByLoginIdPattern(client, company, srcLoginId);
    for (String targetLoginId : srcLoginIds) {
      Partner target = getPartnerByLoginIdPattern(client, company, targetLoginId);
      Objects.assertNull(target.getParentId(), "{0} - parentId must be null!", targetLoginId);
      Objects.assertNull(target.getOrgPartnerId(), "{0} - orgPartnerId must be null!", targetLoginId);
      srcPartner.merge(target);
      target.setMovePartnerId(srcPartner.getId());
      target.setStorageState(StorageState.DEPRECATED);
      savePartner(client, company, target);
    }
    return savePartner(client, company, srcPartner);
  }

  public Partner switchPartnerAccountType(ClientContext client, ICompany company, SwitchPartnerAccountTypeModel model) {
    Partner partner = model.getPartner();
    Account account = accountLogic.getAccountById(client, partner.getAccountId());
    Objects.assertNotNull(account, "Partner Account is not found!, name = {}", partner.getName());
    final String loginId = account.getLoginId();

    UserProfile newUserProfile = model.getUserProfile();
    if (Objects.nonNull(newUserProfile)) {
      UserProfile userProfile = profileLogic.getUserProfile(client, loginId);
      if (userProfile != null) {
        userProfile.merge(newUserProfile);
      } else {
        userProfile = newUserProfile;
      }
      profileLogic.saveUserProfile(client, userProfile);
      profileLogic.deleteOrgProfile(client, loginId);
      partner.setPartnerAccountType(AccountType.USER);
      account.setAccountType(AccountType.USER);
    } else {
      OrgProfile newOrgProfile = model.getOrgProfile();
      OrgProfile orgProfile = profileLogic.getOrgProfile(client, loginId);
      if (orgProfile != null) {
        orgProfile.merge(newOrgProfile);
      } else {
        orgProfile = newOrgProfile;
      }
      profileLogic.saveOrgProfile(client, orgProfile);
      profileLogic.deleteUserProfile(client, loginId);
      partner.setPartnerAccountType(AccountType.ORGANIZATION);
      account.setAccountType(AccountType.ORGANIZATION);
    }
    accountLogic.getAccountRepo().save(account);
    return savePartner(client, company, partner);
  }

  public Boolean deletePartner(ClientContext client, ICompany company, Partner partner) {
    // Delete partner, account associate with partner
    // If account associate with another employee, only delete partner

    partnerRepo.delete(partner);
    final Account account = accountLogic.getAccountById(client, partner.getAccountId());
    final String loginId = account.getLoginId();
    // EmployeeLogic employeeLogic = context.getBean(EmployeeLogic.class);
    // final Employee employee = employeeLogic.getEmployee(client, company,
    // loginId);
    // Objects.assertNull(employee, "Partner must not be linked with employee!,
    // loginId = ", loginId);
    accountLogic.deleteAccountById(client, partner.getAccountId());
    return true;
  }

  public List<SqlMapRecord> searchPartners(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/partner/groovy/PartnerSql.groovy";
    sqlParams.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchPartners", sqlParams);
  }

  public List<AllowAccessCompany> findPartnerCompanyAcls(ClientContext client, AccessType accessType, String loginId) {
    SqlQuery query = new SqlQuery()
        .ADD_TABLE(
            new EntityTable(Partner.class)
                .addSelectField("companyPriority", "priority"),
            new EntityTable(Account.class)
                .addSelectField("loginId", "loginId"),
            new EntityTable(Company.class)
                .addSelectField("id", "companyId")
                .addSelectField("parentId", "companyParentId")
                .addSelectField("code", "companyCode")
                .addSelectField("label", "companyLabel"))
        .FILTER(new ClauseFilter(Partner.class, "companyId", "=", Company.class, "id"))
        .FILTER(new ClauseFilter(Partner.class, "accountId", "=", Account.class, "id"))
        .FILTER(new ClauseFilter(Account.class, "loginId", "=", ":loginId"))
        .FILTER(new OptionFilter(Partner.class, "storageState", "=", StorageState.ALL).value(StorageState.ACTIVE))
        .ORDERBY(new String[] { "priority" }, "priority", "ASC");
    query.addParam("loginId", loginId);
    List<AllowAccessCompany> acls = query(client, query, AllowAccessCompany.class);
    return acls;
  }

  public boolean changeStorageStates(ClientContext client, ChangeStorageStateRequest req) {
    List<Partner> partners = partnerRepo.findPartners(req.getEntityIds());
    for (Partner partner : partners) {
      changeStorageState(client, partner, req.getNewStorageState());
    }
    return true;
  }

  public boolean changeStorageState(ClientContext client, Partner partner, StorageState state) {
    plugins.forEach(plugin -> {
      plugin.onPreStateChange(client, partner, state);
    });
    partnerRepo.setPartnersState(state, partner.getId());
    plugins.forEach(plugin -> {
      plugin.onPostStateChange(client, partner, state);
    });
    return true;
  }

  public boolean changePartnerTypesStorageState(ClientContext client, ChangeStorageStateRequest req) {
    List<PartnerType> partnerTypes = repo.findPartnerTypes(req.getEntityIds());
    for (PartnerType partnerType : partnerTypes) {
      repo.setPartnerTypesState(req.getNewStorageState(), partnerType.getType());
    }
    return true;
  }

  public PartnerType savePartnerType(ClientContext clientCtx, PartnerType type) {
    type.set(clientCtx);
    repo.save(type);
    return null;
  }

  public PartnerType getPartnerType(String type) {
    return repo.getByType(type);
  }

  public List<PartnerType> searchPartnerTypes(ClientContext client, SqlQueryParams params) {
    SqlQuery query = new SqlQuery().ADD_TABLE(new EntityTable(PartnerType.class).selectAllFields()).FILTER(
        SearchFilter.isearch(PartnerType.class, "type")).FILTER(
            OptionFilter.storageState(PartnerType.class),
            RangeFilter.createdTime(PartnerType.class),
            RangeFilter.modifiedTime(PartnerType.class))
        .ORDERBY(new String[] { "type", "modifiedTime" }, "modifiedTime", "DESC");
    query.mergeValue(params);
    return query(client, query, PartnerType.class);
  }

  public Partner onAddPartnerType(ClientContext client, ICompany company, PartnerType type, Partner partner) {
    PartnerTypePlugin plugin = partnerTypePlugins.get(type.getType());
    plugin.onAddPartnerType(client, company, partner);
    partner.withPartnerType(type);
    return partnerRepo.save(partner);
  }

  public Partner onRemovePartnerType(ClientContext client, ICompany company, PartnerType type, Partner partner) {
    PartnerTypePlugin plugin = partnerTypePlugins.get(type.getType());
    plugin.onRemovePartnerType(client, company, partner);
    partner.removePartnerType(type);
    return partnerRepo.save(partner);
  }

  public void savePartnerTypePlugin(
      ClientContext client, ICompany company, String partnerType, Long partnerId, MapObject dataMapping) {
    PartnerTypePlugin plugin = partnerTypePlugins.get(partnerType);
    Objects.assertNotNull(plugin, "Partner ContactType Plugin not found: " + partnerType);
    plugin.updatePartnerPlugin(client, company, partnerId, dataMapping);
  }

}