package net.datatp.module.dtable;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.dtable.entity.DTable;
import net.datatp.module.dtable.entity.DTableDynamicRow;
import net.datatp.module.dtable.entity.DTablePlugin;
import net.datatp.module.dtable.entity.DTableRow;
import net.datatp.module.dtable.plugin.DTablePluginLogic;
import net.datatp.module.dtable.plugin.PluginExecuteModel;
import net.datatp.module.dtable.plugin.PluginOutput;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;

@Service("DTableService")
public class DTableService {
  @Autowired
  private DTableLogic logic;
  
  @Autowired @Getter
  private EmployeeLogic employeeLogic;
  
  @Autowired @Getter
  private AccountLogic accountLogic;

  @Autowired
  private DTablePluginLogic pluginLogic;

  public DTable getDTable(ClientContext client, ICompany company, String code) {
    return logic.getDTableByCode(client, company, code);
  }

  @Transactional
  public DTable saveDTable(ClientContext client, ICompany company, DTable dtable) {
    return logic.saveDTable(client, company, dtable);
  }

  @Transactional
  public List<DTable> deleteDTable(ClientContext client, ICompany company, List<DTable> dtables) {
    return logic.deleteDTable(client, company, dtables);
  }

  @Transactional
  public DTable deleteDTableColumns(ClientContext client, ICompany company, DeleteColumnModel model) {
    return logic.deleteDTableColumn(client, company, model);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchDTables(ClientContext client, ICompany company, SqlQueryParams params) {
    return logic.searchDTables(client, company, params);
  }

  @Transactional
  public DTableRow saveSpreadsheetRow(ClientContext client, ICompany company, DTableRow row) {
    return logic.saveSpreadsheetRow(client, company, row);
  }

  @Transactional(readOnly = true)
  public List<MapObject> searchDynamicDTableRows(ClientContext client, ICompany company, String code, SqlQueryParams params) {
    return logic.searchDynamicDTableRows(client, company, code, params);
  }
  
  @Transactional(readOnly = true)
  public MapObject getDynamicDTableRowById(ClientContext client, ICompany company, Long rowId) {
    return logic.getDynamicDTableRowById(client, company, rowId);
  }
  
  @Transactional(readOnly = true)
  public MapObject getDynamicDTableRowByCellStringValue(ClientContext client, ICompany company, CellParam param) {
      return logic.getDynamicDTableRowByCellStringValue(client, company, param);
  }

  @Transactional
  public List<DTableDynamicRow> saveDTableRows(ClientContext client, ICompany company,String spreadsheetCode, List<DTableDynamicRow> dRows) {
    return logic.saveDTableRows(client, company, spreadsheetCode, dRows);
  }

  @Transactional
  public PluginOutput pluginExecute(ClientContext client, ICompany company, PluginExecuteModel model) {
    return pluginLogic.execute(client, company, model);
  }

  @Transactional(readOnly = true)
  public List<DTablePlugin> findAvailablePlugins(ClientContext client, ICompany company) {
    return pluginLogic.findAvailablePlugins(client, company);
  }

  @Transactional
  public List<MapObject> importXlsx(ClientContext client, ICompany company, DTable DTable, String xlsxRes) {
    return logic.importXlsx(client, company, DTable, xlsxRes);
  }

  @Transactional
  public List<MapObject> importXlsx(ClientContext client, ICompany company, String spreadsheetCode, UploadResource uploadResource) {
    return logic.importXlsx(client, company, spreadsheetCode, uploadResource);
  }
  
  @Transactional
  public boolean updateDTableRowStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return logic.updateDTableRowStorageState(client, req);
  }
}