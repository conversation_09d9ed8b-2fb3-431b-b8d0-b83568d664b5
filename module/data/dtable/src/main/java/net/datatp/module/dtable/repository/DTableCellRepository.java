package net.datatp.module.dtable.repository;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.data.db.repository.DataTPRepository;
import net.datatp.module.dtable.entity.DTableCell;

public interface DTableCellRepository extends DataTPRepository<DTableCell, Serializable> {
  @Query("Select c FROM DTableCell c WHERE c.companyId = :companyId AND c.rowId = :rowId")
  List<DTableCell> findByRowId(@Param("companyId") Long companyId, @Param("rowId") Long rowId);

  @Query("Select c FROM DTableCell c WHERE c.companyId = :companyId "
      + "AND c.dtableId = :dtableId AND c.columnId = :columnId AND c.stringValue = :value")
  List<DTableCell> findByCellStringValue(
      @Param("companyId") Long companyId,
      @Param("dtableId") Long dtableId,
      @Param("columnId") String columnId,
      @Param("value") String value);

  @Modifying
  @Query("DELETE FROM DTableCell sc WHERE sc.dtableId = :dtableId AND sc.columnId IN (:columnIds)")
  void deleteCellByDTableIdAndColumnId(@Param("dtableId") Long dtableId, @Param("columnIds") List<String> columnIds);

  @Modifying
  @Query("DELETE FROM DTableCell sc WHERE sc.dtableId = :dtableId")
  void deleteCellByDTableId(@Param("dtableId") Long dtableId);
}