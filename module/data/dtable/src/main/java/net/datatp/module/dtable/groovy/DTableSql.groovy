package net.datatp.module.dtable.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject

public class DTableSql extends Executor {

  public class SearchDTableRow extends ExecutableSqlBuilder {

    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT
          *
        FROM data_dtable_row r
        WHERE
            ${FILTER_BY_STORAGE_STATE('r', sqlParams)}
            ${AND_FILTER_BY_PARAM('r.company_id', 'companyId', sqlParams)}
            ${AND_FILTER_BY_PARAM('r.dtable_id', 'dtableId', sqlParams)}
            ${AND_FILTER_BY_PARAM('r.owner_account_id', 'ownerAccountId', sqlParams)}
            ${MAX_RETURN(sqlParams)}
      """;
      return query;
    }
  }

  public DTableSql() {
    register(new SearchDTableRow());
  }
}
