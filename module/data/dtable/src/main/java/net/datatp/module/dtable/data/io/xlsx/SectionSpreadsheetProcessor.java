package net.datatp.module.dtable.data.io.xlsx;

import lombok.extern.slf4j.Slf4j;
import net.datatp.module.data.xlsx.IXLSXSectionProcessorPlugin;
import net.datatp.module.data.xlsx.IXLSXSheetProcessor;
import net.datatp.module.data.xlsx.SectionContext;
import net.datatp.module.data.xlsx.XLSXProcessMode;
import net.datatp.module.data.xlsx.XLSXRow;
import net.datatp.module.data.xlsx.XLSXSectionProcessor;
import net.datatp.module.data.xlsx.XSLXCell;
import net.datatp.module.data.xlsx.XSLXCell.CellMapper;
import net.datatp.module.dtable.entity.DTable;
import net.datatp.module.dtable.entity.DTableColumn;
import net.datatp.module.dtable.entity.DTableRow;
import net.datatp.util.text.StringUtil;

@Slf4j
public class SectionSpreadsheetProcessor extends XLSXSectionProcessor<DTableRow> {

  public SectionSpreadsheetProcessor(DTable dTable) {
    dTable.getColumnMap().forEach((key, column) -> add(columnMapper(column)));
  }

  private XSLXCell<DTableRow> columnMapper(DTableColumn column) {
    String label = column.getLabel().trim();
    final XSLXCell<DTableRow> spreadsheetRowXSLXCell = new XSLXCell<>(label, false);

    final CellMapper<DTableRow> spreadsheetRowCellMapper = (ctx, row, entity, cell) -> {
      if (StringUtil.isEmpty(cell)) return;
      try {
        entity.mapCellValue(column.getColumnId(), column.getType(), cell);
      } catch(Throwable error) {
        log.error("Error on map cell {} with value {} ", column.getLabel(), cell);
        throw error;
      }
    };
    return spreadsheetRowXSLXCell.mapper(spreadsheetRowCellMapper);
  }

  public void mapRow(SectionContext ctx, XLSXRow row) throws Exception {
    entity = new DTableRow();
    mapRow(ctx, row, entity);
    collect(entity);
  }

  protected void onStartSection(IXLSXSheetProcessor sheetProcessor, SectionContext ctx) throws Exception {
    newCollector();
  }

  public void initDefaultStorePlugin() {
    setPlugin(new DefaultStorePlugin());
  }

  private class DefaultStorePlugin extends IXLSXSectionProcessorPlugin {
    @Override
    public void onProcessRow(IXLSXSheetProcessor sheetProcessor, SectionContext ctx, XLSXRow row, XLSXProcessMode mode) throws Exception {
      mapRow(ctx, row);
    }
  }
}