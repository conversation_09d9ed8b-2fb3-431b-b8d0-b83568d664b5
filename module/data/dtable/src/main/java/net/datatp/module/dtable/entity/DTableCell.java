package net.datatp.module.dtable.entity;

import java.io.Serial;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyPersistable;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;

@Entity
@Table(
  name = DTableCell.TABLE_NAME,
  indexes = { @Index(columnList="column_id,string_value") }
)
@Getter @Setter @NoArgsConstructor
public class DTableCell extends CompanyPersistable {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "data_dtable_cell";

  @Column(name="dtable_id")
  private Long dtableId;

  @Column(name="row_id", updatable=false, insertable=false)
  private Long rowId;

  @NotNull
  @Column(name="column_id")
  private String columnId;

  @Enumerated(EnumType.STRING)
  @Column(name = "cell_type")
  private DTableCellType cellType;

  @Column(name = "string_value", length = 2 * 1024)
  private String  stringValue;

  @Column(name = "double_value")
  private double  doubleValue;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "date_value")
  private Date  dateValue;

  public DTableCell(String columnId, String value) {
    setCellValue(columnId, value);
  }

  public DTableCell(String columnId, double value) {
    setCellValue(columnId, value);
  }

  public DTableCell(String columnId, Date value) {
    setCellValue(columnId, value);
  }

  public DTableCell(String columnId, boolean value) {
    setCellValue(columnId, value);
  }

  public DTableCell(String columnId, DTableCellType type, String value) {
    setCellValue(columnId, type, value);
  }

  public void setCellValue(String columnId, String value) {
    this.columnId = columnId;
    this.stringValue = value;
    this.cellType = DTableCellType.String;
  }

  public void setCellValue(String columnId, double value) {
    this.columnId = columnId;
    this.doubleValue = value;
    this.cellType = DTableCellType.Double;
  }

  public void setCellValue(String columnId, int value) {
    this.columnId = columnId;
    this.doubleValue = value;
    this.cellType = DTableCellType.Integer;
  }

  public void setCellValue(String columnId, Date value) {
    this.columnId = columnId;
    this.dateValue = value;
    this.cellType = DTableCellType.Date;
  }

  public void setCellValue(String columnId, boolean value) {
    this.columnId = columnId;
    this.stringValue = Boolean.toString(value);
    this.cellType = DTableCellType.Boolean;
  }

  public void setCellValue(String columnId, DTableCellType type, String value) {
    if(DTableCellType.Boolean.equals(type)) {
      setCellValue(columnId, Boolean.parseBoolean(value));
    } else if(DTableCellType.Double.equals(type)) {
      setCellValue(columnId, Double.parseDouble(value));
    } else if(DTableCellType.Date.equals(type)) {
      if(value.length() == DateUtil.LOCAL_DATETIME_FORMAT.length()) {
        setCellValue(columnId, DateUtil.parseLocalDateTime(value));
      } else if(value.length() == DateUtil.COMPACT_DATE_FORMAT.length()) {
        setCellValue(columnId, DateUtil.parseCompactDate(value));
      } else  {
        setCellValue(columnId, DateUtil.parseCompactDateTime(value));
      }
    } else if(DTableCellType.Integer.equals(type)) {
      setCellValue(columnId, Integer.parseInt(value));
    } else if(DTableCellType.String.equals(type)) {
      setCellValue(columnId, value);
    } else {
      throw RuntimeError.IllegalState("Unknown cell type for columnId = {0}, type = {1}, value = {2}", columnId, type, stringValue);
    }
  }

  public boolean cellBooleanValue() {
    if(DTableCellType.Boolean.equals(cellType)) return Boolean.parseBoolean(stringValue);
    throw RuntimeError.IllegalState("Unknown cell type for name = {0}, value = {1}", columnId, stringValue);
  }

  public String cellStringValue() {
    if(DTableCellType.String.equals(cellType)) return stringValue;
    throw RuntimeError.IllegalState("Unknown cell type for name = {0}, value = {1}", columnId, stringValue);
  }

  public double cellDoubleValue() {
    if(DTableCellType.Double.equals(cellType)) return doubleValue;
    throw RuntimeError.IllegalState("Unknown cell type for name = {0}, value = {1}", columnId, stringValue);
  }

  public int cellIntegerValue() {
    if(DTableCellType.Integer.equals(cellType)) return (int)doubleValue;
    throw RuntimeError.IllegalState("Unknown cell type for name = {0}, value = {1}", columnId, stringValue);
  }

  public String cellDateValue() {
    if(DTableCellType.Date.equals(cellType)) return DateUtil.asCompactDateTime(dateValue);
    throw RuntimeError.IllegalState("Unknown cell type for name = {0}, value = {1}", columnId, stringValue);
  }

  public Object cellValue() {
    if(DTableCellType.String.equals(cellType)) return stringValue;
    else if(DTableCellType.Double.equals(cellType)) return doubleValue;
    else if(DTableCellType.Date.equals(cellType)) return DateUtil.asCompactDateTime(dateValue);
    else if(DTableCellType.Boolean.equals(cellType)) return Boolean.valueOf(stringValue);
    throw RuntimeError.IllegalState("Unknown cell type for name = {0}, value = {1}", columnId, stringValue);
  }

  public String identify() { return columnId; }
}