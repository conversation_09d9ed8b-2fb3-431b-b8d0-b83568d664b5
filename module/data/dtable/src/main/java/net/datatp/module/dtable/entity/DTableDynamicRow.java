package net.datatp.module.dtable.entity;

import java.io.Serial;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnore;

import net.datatp.module.data.db.entity.DynamicEntity;
import net.datatp.module.data.db.entity.EditState;
import net.datatp.module.data.db.entity.Persistable;
import net.datatp.util.ds.Objects;

public class DTableDynamicRow extends DynamicEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public <T extends Persistable<Long>> void update(DTable DTable, DTableRow entity) {
    if(!entity.isNew()) {
      Objects.assertEquals(id(), entity.getId());
    }

    Map<String, DTableColumn> columnMap = DTable.getColumnMap();
    for(String field : this.keySet()) {

      if("dtableId".equals(field)) {
      } else if("ownerAccountId".equals(field)) {
        entity.setOwnerAccountId(getLong(field, null));
      } else if("ownerLabel".equals(field)) {
        entity.setOwnerLabel(getString(field, null));
      } else if("editState".equals(field)) {
        entity.setEditState(EditState.valueOf(getString(field, "NEW")));
      } else if("id".equals(field)) {
      } else if("_rowHeight_".equals(field)) {
        entity.setRowHeight(getInteger(field, 0));
      } else {
        DTableColumn column = columnMap.get(field);
        entity.mapCellValue(field, column.getType(), getString(field, null));
      }
    }
  }

  @JsonIgnore
  public Long getOwnerAccountId() { return getLong("ownerAccountId", null); }
  public String getOwnerLabel() { return getString("ownerLabel", null); }
}