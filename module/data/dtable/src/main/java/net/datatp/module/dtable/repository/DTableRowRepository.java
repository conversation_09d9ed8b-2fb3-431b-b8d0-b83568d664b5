package net.datatp.module.dtable.repository;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.repository.DataTPRepository;
import net.datatp.module.dtable.entity.DTableRow;

public interface DTableRowRepository extends DataTPRepository<DTableRow, Serializable> {
  @Query("Select r FROM DTableRow r WHERE r.id = :id")
  DTableRow getById(@Param("id") Long id);

  @Query("Select r FROM DTableRow r WHERE r.id IN :ids")
  List<DTableRow> findByIds(@Param("ids") Collection<Long> ids);

  @Query("Select r FROM DTableRow r WHERE r.dtableId = :dtableId")
  List<DTableRow> findByDTable(@Param("dtableId") Long dtableId);

  @Modifying
  @Query("UPDATE DTableRow t SET t.storageState = :storageState WHERE t.id IN :ids ")
  int updateStorageState(@Param("storageState") StorageState state, @Param("ids") List<Long> ids);
}