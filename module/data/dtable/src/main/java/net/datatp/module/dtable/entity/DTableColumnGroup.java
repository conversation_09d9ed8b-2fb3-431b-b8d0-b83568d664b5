package net.datatp.module.dtable.entity;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.security.client.ClientContext;
import net.datatp.module.company.entity.CompanyPersistable;
import net.datatp.security.client.Capability;

@Entity
@Table(
  name = DTableColumnGroup.TABLE_NAME,
  indexes = { @Index(columnList="name") }
)
@Getter @Setter @NoArgsConstructor
public class DTableColumnGroup extends CompanyPersistable {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "data_dtable_column_group";

  private int     idx;
  private String  name;
  private String  label;
  private boolean visible;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "column_group_id", referencedColumnName = "id")
  @OrderBy("idx")
  private List<DTableColumn> columns = new ArrayList<>();

  @Enumerated(EnumType.STRING)
  @Column(name = "min_column_access_capability")
  private Capability minColumnAccessCapability = Capability.None;

  public DTableColumnGroup(String name, String label) {
    this.name  = name;
    this.label = label;
  }

  public DTableColumn addColumn(String label, DTableCellType type) {
    DTableColumn column = new DTableColumn(label, type);
    columns.add(column);
    return column;
  }

  public String identify() { return name; }

  @Override
  public void set(ClientContext client, Long companyId) {
    super.set(client, companyId);
    for(DTableColumn sel : columns) {
      sel.set(client, companyId);
    }
  }

  @Override
  public DTableColumnGroup clearIds() {
    setId(null);
    clearIds(this.columns);
    return this;
  }
}