package net.datatp.module.dtable.entity;

import java.io.Serial;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.security.client.Capability;
import net.datatp.module.core.security.entity.CompanyLoginPermission;
import net.datatp.module.hr.entity.Employee;

@Entity
@Table(
    name = DTablePermission.TABLE_NAME,
    uniqueConstraints = {
      @UniqueConstraint(columnNames = {"dtable_id", "user_id"})
    },
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class DTablePermission extends CompanyLoginPermission {

  final static public String TABLE_NAME = "data_dtable_permission";

  @Serial
  private static final long serialVersionUID = 1L;

  @Column(name="dtable_id", updatable=false, insertable=false)
  private Long dtableId;

  public DTablePermission(Employee employee, Capability cap) {
    super(employee.getId(), employee.getLabel(), cap);
    this.type = Type.Employee;

  }

  public DTablePermission(Type type, Long id, Capability cap) {
    super(id, id + "", cap);
    this.type = type;
  }
}