package net.datatp.module.dtable;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.ConditionFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.Join;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.xlsx.XLSXSimpleSheetProcessor;
import net.datatp.module.data.xlsx.XSLXDataSheetProcessor;
import net.datatp.module.dtable.data.io.xlsx.SectionSpreadsheetProcessor;
import net.datatp.module.dtable.entity.DTable;
import net.datatp.module.dtable.entity.DTableCell;
import net.datatp.module.dtable.entity.DTableCellType;
import net.datatp.module.dtable.entity.DTableColumn;
import net.datatp.module.dtable.entity.DTableColumnGroup;
import net.datatp.module.dtable.entity.DTableDynamicRow;
import net.datatp.module.dtable.entity.DTablePermission;
import net.datatp.module.dtable.entity.DTableRow;
import net.datatp.module.dtable.repository.DTableCellRepository;
import net.datatp.module.dtable.repository.DTableRepository;
import net.datatp.module.dtable.repository.DTableRowRepository;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.module.http.upload.UploadService;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.io.IOUtil;

@Slf4j
@Component
public class DTableLogic extends DAOService {

  @Autowired
  private DTableRepository repo;

  @Autowired @Getter
  private DTableRowRepository rowRepo;

  @Autowired
  private DTableCellRepository cellRepo;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private UploadService uploadService;

  @Autowired
  private SecurityLogic securityLogic;

  public DTable getDTableByCode(ClientContext client, ICompany company, String code) {
    return repo.getByCode(company.getId(), code);
  }

  public DTable getDTableById(ClientContext client, ICompany company, Long id) {
    return repo.getById(id);
  }

  public List<DTable> findDTables(ClientContext client, ICompany company) {
    return repo.findByCompanyId(company.getId());
  }

  public DTable saveDTable(ClientContext client, ICompany company, DTable dTable) {
    java.util.Collections.sort(dTable.getColumnGroups(), new Comparator<DTableColumnGroup>() {
      @Override
      public int compare(DTableColumnGroup o1, DTableColumnGroup o2) {
        return o1.getIdx()-o2.getIdx();
      }
    });
    dTable.updateColumnIndices();
    dTable.set(client, company);
    return repo.save(client, company, dTable);
  }

  public List<DTable> deleteDTable(ClientContext client, ICompany company, List<DTable> dtables) {
    for (DTable sheet : dtables) {
      List<DTableRow> rows = rowRepo.findByDTable(sheet.getId());
      for (DTableRow row : rows) {
        List<DTableCell> cells = cellRepo.findByRowId(company.getId(), row.getId());
        cellRepo.deleteAll(cells);
      }
      rowRepo.deleteAll(rows);
      repo.delete(sheet);
    }
    return dtables;
  }

  public DTable deleteDTableColumn(ClientContext client, ICompany company, DeleteColumnModel model) {
    DTable DTable = model.getDtable();
    List<DTableColumn> columns = model.getColumns();
    if(Collections.isEmpty(columns)) columns = model.getColumnGroup().getColumns();
    List<String> columnIds = Collections.transform(columns, DTableColumn::getColumnId);
    cellRepo.deleteCellByDTableIdAndColumnId(DTable.getId(), columnIds);
    List<DTableColumnGroup> columnGroups = DTable.getColumnGroups();
    Iterator<DTableColumnGroup> iterator = columnGroups.iterator();
    while (iterator.hasNext()){
      DTableColumnGroup gr = iterator.next();
      if(gr.getId() == model.getColumnGroup().getId()) {
        for(DTableColumn column: columns) {
          Collections.remove(gr.getColumns(), (col) -> col.getId() == column.getId());
        }
        if(Collections.isEmpty(gr.getColumns())) iterator.remove();
      }
    }
    return saveDTable(client, company, DTable);
  }

  public List<SqlMapRecord> searchDTables(ClientContext client, ICompany company, SqlQueryParams params) {
    boolean moderator = securityLogic.hasModeratorPermission(client, company.getId(), "spreadsheet", "spreadsheet");
    if(!moderator) {
      params.addParam("participant", client.getRemoteUser());
    }

    EntityTable entityTable = new EntityTable(DTable.class).selectAllFields();
    Set<String> selectedField = entityTable.getSelectFieldMap().keySet();

    String[] SEARCH_FIELDS = {"code", "label", "ownerLabel"};
    params.addParam("companyId", company.getId());
    SqlQuery query = new SqlQuery()
      .ADD_TABLE(entityTable)
      .FILTER(ClauseFilter.company(DTable.class))
      .FILTER(SearchFilter.isearch(DTable.class, SEARCH_FIELDS))
      .FILTER(
        OptionFilter.storageState(DTable.class),
        RangeFilter.modifiedTime(DTable.class),
        RangeFilter.createdTime(DTable.class))
      .ORDERBY(new String[]{"modifiedTime"}, "modifiedTime", "DESC");

    if(!moderator) {
      query
        .JOIN(
          new Join("LEFT JOIN", DTablePermission.class)
            .ON("dtableId", DTable.class, "id"))
        .FILTER(
          new ConditionFilter(DTable.class, "ownerAccountId", "= :participant")
            .hasVariableCondition("participant"))
        .GROUPBY(selectedField.toArray(new String[0]));
    }
    return query(client, query, params).getSqlMapRecords();
  }

  public DTableRow saveSpreadsheetRow(ClientContext client, ICompany company, DTableRow row) {
    row.set(client, company);
    return rowRepo.save(row);
  }

  public List<MapObject> searchDynamicDTableRows(ClientContext client, ICompany company, String code, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/dtable/groovy/DTableSql.groovy";
    params.addParam("companyId", company.getId());
    DTable dTable = getDTableByCode(client, client.getCompany(), code);
    Objects.assertNotNull(dTable, "DTable is not found by code = {}", code);
    params.addParam("dtableId", dTable.getId());
    List<SqlMapRecord> records = searchDbRecords(client, scriptDir, scriptFile, "SearchDTableRow", params);
    List<DTableRow> rows = rowRepo.findByIds(SqlMapRecord.getIds(records));
    return Collections.transform(rows, sel -> mappingDynamicRow(sel));
  }

  public MapObject getDynamicDTableRowById(ClientContext client, ICompany company, Long rowId) {
    DTableRow row = rowRepo.getById(rowId);
    return mappingDynamicRow(row);
  }

  public MapObject getDynamicDTableRowByCellStringValue(ClientContext client, ICompany company, CellParam param) {
    DTable dTable = getDTableByCode(client, company, param.getSpreadsheetCode());
    List<DTableCell> cells = cellRepo.findByCellStringValue(company.getId(), dTable.getId(), param.getColumnId(), param.getValue());
    if(cells == null) return null;
    if(cells.size() > 1) RuntimeError.UnknownError("Query returns more than 1 record");
    DTableCell cell = cells.get(0);
    DTableRow row = rowRepo.getById(cell.getRowId());
    return mappingDynamicRow(row);
  }

  public MapObject mappingDynamicRow(DTableRow row) {
    MapObject dRow = new MapObject();
    dRow.put("id", row.getId());
    dRow.put("dtableId", row.getDtableId());
    dRow.put("ownerAccountId", row.getOwnerAccountId());
    dRow.put("ownerLabel", row.getOwnerAccountId() != null ? row.getOwnerLabel().toUpperCase() : "N/A");
    dRow.put("_rowHeight_", row.getRowHeight());
    for (Map.Entry<String, DTableCell> entry : row.getCellMap().entrySet()) {
      DTableCell cell = entry.getValue();
      String columnId = cell.getColumnId();

      DTableCellType type = cell.getCellType();
      if (DTableCellType.Boolean.equals(type)) {
        dRow.put(columnId, cell.cellBooleanValue());
      } else if (DTableCellType.Integer.equals(type)) {
        dRow.put(columnId, cell.cellIntegerValue());
      } else if (DTableCellType.Double.equals(type)) {
        dRow.put(columnId, cell.cellDoubleValue());
      } else if (DTableCellType.Date.equals(type)) {
        dRow.add(columnId,  cell.cellDateValue());
      } else {
        dRow.put(columnId, cell.cellStringValue());
      }
    }
    return dRow;
  }

  public List<DTableDynamicRow> saveDTableRows(
    ClientContext client, ICompany company, String spreadsheetCode, List<DTableDynamicRow> dRows) {
    Iterator<DTableDynamicRow> i = dRows.iterator();
    DTable dTable = getDTableByCode(client, company, spreadsheetCode);
    Account account = accountLogic.getAccountById(client, client.getAccountId());
    while (i.hasNext()) {
      DTableDynamicRow dRow = i.next();
      Long id = dRow.id();
      if (dRow.getEditState().equals("DELETED")) {
        rowRepo.deleteById(id);
        i.remove();
        continue;
      }

      DTableRow row;
      if (id != null) {
        row = rowRepo.getById(id);
      } else {
        row = new DTableRow(dTable);
        row.setOwnerAccountId(client.getAccountId());
        row.setOwnerLabel(account.getFullName());
      }
      row.getCellMap().forEach((key, value) -> { value.setDtableId(dTable.getId()); });
      dRow.update(dTable, row);
      row = saveSpreadsheetRow(client, company, row);
      dRow.put("id", row.getId());
      dRow.put("dtableId", row.getDtableId());
      dRow.put("ownerAccountId", row.getOwnerAccountId());
      dRow.put("ownerLabel", row.getOwnerAccountId() != null ? row.getOwnerLabel().toUpperCase() : "N/A");
    }
    return dRows;
  }

  public List<MapObject> importXlsx(ClientContext client, ICompany company, String spreadsheetCode, UploadResource resource) {
    try {
      byte[] data = uploadService.load(resource.getStoreId());
      InputStream is = new ByteArrayInputStream(data);
      DTable DTable = getDTableByCode(client, company, spreadsheetCode);
      return importXlsx(client, company, DTable, is);
    } catch(Throwable error) {
      throw RuntimeError.UnknownError("Cannot import {0}, error {1}", resource, error);
    }
  }

  public List<MapObject> importXlsx(ClientContext client, ICompany company, DTable DTable, String xlsxRes) {
    try {
      if(!IOUtil.hasResource(xlsxRes)) return new ArrayList<>();
      InputStream inputstream = IOUtil.loadResource(xlsxRes);
      return importXlsx(client, company, DTable, inputstream);
    } catch(Throwable error) {
      throw RuntimeError.UnknownError("Cannot import {}", xlsxRes);
    }
  }

  public List<MapObject> importXlsx(ClientContext client, ICompany company, DTable dTable, InputStream inputstream) throws Exception {
    SectionSpreadsheetProcessor sectionProcessor = new SectionSpreadsheetProcessor(dTable);
    sectionProcessor.initDefaultStorePlugin();
    XLSXSimpleSheetProcessor sheetProcessor = new XLSXSimpleSheetProcessor(sectionProcessor);

    ZipSecureFile.setMinInflateRatio(0);
    XSLXDataSheetProcessor xlsxProcessor =
      new XSLXDataSheetProcessor()
        .open(inputstream)
        .addAttribute(client);

    for(String sheetName : xlsxProcessor.getWorkbook().getAllSheetNames()) {
      if(sheetName.startsWith("__")) continue;
      xlsxProcessor.process(sheetName, sheetProcessor, false);
    }
    xlsxProcessor.close();

    Account account = accountLogic.getAccountById(client, client.getAccountId());
    List<DTableRow> rows = sectionProcessor.getCollector();
    for(DTableRow row : rows) {
      row.setOwnerAccountId(client.getAccountId());
      row.setOwnerLabel(account.getFullName());
      row.setDtableId(dTable.getId());
      saveSpreadsheetRow(client, company, row);
    }
    return Collections.transform(rows, sel -> mappingDynamicRow(sel));
  }

  public boolean updateDTableRowStorageState(ClientContext client, ChangeStorageStateRequest req) {
    rowRepo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
}