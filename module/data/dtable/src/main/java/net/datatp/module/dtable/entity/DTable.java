package net.datatp.module.dtable.entity;

import java.io.Serial;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.security.client.ClientContext;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.security.client.Capability;

//TODO: rename class to DTable
//TODO: rename table to data_dtable
//TODO: rename field spreadsheet_* to dtable_*

@Entity
@Table(
  name = DTable.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = DTable.TABLE_NAME + "_code",
      columnNames = {"company_id", "code"})
  },
  indexes = { @Index(columnList="code") }
)
@Getter @Setter @NoArgsConstructor
public class DTable extends CompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "data_dtable";

  @NotNull
  @Column(length = 50)
  private String code;

  @NotNull
  private String label;

  @Column(name = "owner_account_id")
  private Long ownerAccountId;

  @Column(name = "owner_label")
  private String ownerLabel;

  @Column(length = 1024 * 32)
  private String description;

  @Column(name = "summary_support")
  private Boolean summarySupport;

  @Column(name = "summary_cell_height")
  private Integer summaryCellHeight;

  @Enumerated(EnumType.STRING)
  @Column(name = "min_row_access_capability")
  private Capability minRowAccessCapability = Capability.None;

  //TODO: add spreadsheet type
  private String type = "GENERIC";
  
  //TODO: no need
  @Column(name = "js_plugins")
  @Convert(converter = StringSetConverter.class)
  protected Set<String> jsPlugins = new HashSet<>();

  //TODO: rename field spreadsheet_* to dtable_*
  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "dtable_id", referencedColumnName = "id")
  @OrderBy("idx")
  private List<DTableColumnGroup> columnGroups = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "dtable_id", referencedColumnName = "id")
  private List<DTableJSMacro> jsMacros = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "dtable_id", referencedColumnName = "id")
  private List<DTablePlugin> plugins = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "dtable_id", referencedColumnName = "id")
  private List<DTablePermission> permissions  = new ArrayList<>();

  @JsonIgnore
  public Map<String, DTableColumn> getColumnMap() {
    Map<String, DTableColumn> map = new HashMap<>();
    for(DTableColumnGroup columnGroup : columnGroups) {
      for(DTableColumn column : columnGroup.getColumns()) {
        map.put(column.getColumnId(), column);
      }
    }
    return map;
  }

  public DTableColumnGroup addColumnGroup(String name, String label) {
    DTableColumnGroup columnGroup = new DTableColumnGroup(name, label);
    columnGroups.add(columnGroup);
    updateColumnIndices();
    return columnGroup;
  }

  public DTableColumnGroup rmColumnGroup(String name) {
    Iterator<DTableColumnGroup> i = columnGroups.iterator();
    DTableColumnGroup columnGroup = null;
    while(i.hasNext()) {
      DTableColumnGroup selColumnGroup = i.next();
      if(name.equals(selColumnGroup.getName())) {
        i.remove();
        columnGroup = selColumnGroup;
        updateColumnIndices();
        break;
      }
    }
    return columnGroup;
  }

  public void updateColumnIndices() {
    int columnIdx = 0 ;
    for(int i = 0; i < columnGroups.size(); i++) {
      DTableColumnGroup columnGroup  = columnGroups.get(i);
      columnGroup.setIdx(i);
      columnGroup.setName("group-" + i);
      for(DTableColumn column : columnGroup.getColumns()) {
        column.setIdx(columnIdx);
        columnIdx++ ;
      }
    }
  }

  public DTable withJsPlugin(String plugin) {
    if(this.jsPlugins == null) this.jsPlugins = new HashSet<>();
    this.jsPlugins.add(plugin);
    return this;
  }

  @Override
  public void set(ClientContext client, ICompany company) {
    super.set(client, company);
    set(client, company, columnGroups);
    set(client, company, permissions);
    set(client, company, jsMacros);
    set(client, company, plugins);
  }

  @Override
  public DTable clearIds() {
    clearId(this);
    clearIds(this.columnGroups);
    clearIds(this.jsMacros);
    clearIds(this.permissions);
    clearIds(this.plugins);
    return this;
  }

  public String identify() { return code; }
}