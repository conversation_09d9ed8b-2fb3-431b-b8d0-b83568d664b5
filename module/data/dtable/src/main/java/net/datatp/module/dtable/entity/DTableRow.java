package net.datatp.module.dtable.entity;

import java.io.Serial;
import java.util.HashMap;
import java.util.Map;

import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.MapKey;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.security.client.ClientContext;
import net.datatp.util.text.StringUtil;


@Entity
@Table(
  name = DTableRow.TABLE_NAME,
  indexes = { @Index(columnList="dtable_id, owner_account_id") }
)
@Getter @Setter @NoArgsConstructor
public class DTableRow extends CompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "data_dtable_row";

  @NotNull
  @Column(name="dtable_id", updatable=false)
  private Long dtableId;

  @Column(name = "owner_account_id")
  private Long ownerAccountId;

  @Column(name = "owner_label")
  private String ownerLabel;
  
  @Column(name = "row_height")
  private int rowHeight = 0;

  @JsonIgnore
  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
  @Fetch(FetchMode.SUBSELECT)
  @JoinColumn(name = "row_id", referencedColumnName = "id")
  @MapKey(name="columnId")
  @OrderBy("id")
  private Map<String, DTableCell> cellMap = new HashMap<>();

  public DTableRow(DTable sheet) {
    this.dtableId = sheet.getId();
  }

  public void mapCellValue(String columnId, DTableCellType type, String value) {
    if(StringUtil.isEmpty(value)) {
      cellMap.remove(columnId);
      return;
    }
    DTableCell cell = cellMap.get(columnId);
    if(cell == null) {
      cell = new DTableCell(columnId, type, value);
      cellMap.put(columnId, cell);
      cell.setDtableId(dtableId);
    } else {
      cell.setCellValue(columnId, type, value);
    }
  }

  @Override
  public void set(ClientContext client, ICompany company) {
    super.set(client, company);
    set(client, company, cellMap.values());
  }

  public String identify() { return Long.toString(id); }
}