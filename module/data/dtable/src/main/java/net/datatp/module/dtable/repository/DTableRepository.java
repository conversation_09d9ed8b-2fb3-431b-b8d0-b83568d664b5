package net.datatp.module.dtable.repository;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.repository.DataTPRepository;
import net.datatp.module.dtable.entity.DTable;

public interface DTableRepository extends DataTPRepository<DTable, Serializable> {
  @Query("SELECT e from DTable e WHERE e.companyId = :companyId AND e.code = :code")
  DTable getByCode(@Param("companyId") Long companyId, @Param("code") String code);

  @Query("SELECT e from DTable e WHERE e.id = :id")
  DTable getById(@Param("id") Long id);

  @Query("SELECT e from DTable e WHERE e.companyId = :companyId")
  List<DTable> findByCompanyId(@Param("companyId") Long companyId);

  @Modifying
  @Query("update DTable e SET e.storageState = :state WHERE e.id IN :ids")
  int updateStorageState(@Param("ids") Collection<Long> ids, @Param("state") StorageState state);
}