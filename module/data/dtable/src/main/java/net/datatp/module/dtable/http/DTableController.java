package net.datatp.module.dtable.http;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletRequest;
import net.datatp.module.backend.BackendResponse;
import net.datatp.module.company.http.BaseCompanyController;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.dtable.DTableExportModel;
import net.datatp.module.dtable.DTableExportService;
import net.datatp.module.dtable.DTableService;
import net.datatp.module.dtable.entity.DTable;
import net.datatp.module.dtable.entity.DTableDynamicRow;
import net.datatp.module.dtable.entity.DTablePlugin;
import net.datatp.module.dtable.plugin.PluginExecuteModel;
import net.datatp.module.dtable.plugin.PluginOutput;
import net.datatp.module.http.get.StoreInfo;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.module.session.HttpClientSessionService;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;

@ConditionalOnBean(HttpClientSessionService.class)
@Api(value = "datatp", tags = { "dtable" })
@RestController
@RequestMapping("/rest/v1.0.0/dtable")
public class DTableController extends BaseCompanyController {
  @Autowired
  private DTableService service;

  @Autowired
  private DTableExportService exportService;
  
  protected DTableController() {
    super("dtable", "/dtable");
  }

  @ApiOperation(value = "Export DTable Template as Excel", response = StoreInfo.class)
  @PostMapping("export-template")
  public @ResponseBody BackendResponse exportDTableTemplate(HttpServletRequest httpReq, @RequestBody DTable ss) {
    Callable<StoreInfo> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return exportService.exportDTableTemplateAsXlsx(clientCtx, clientCtx.getCompany(), ss);
    };
    return execute(Method.POST, "export-template", executor);
  }

  @ApiOperation(value = "Export DTable Data as Excel", response = StoreInfo.class)
  @PostMapping("export-data")
  public @ResponseBody BackendResponse exportDTableData(
      HttpServletRequest httpReq, @RequestBody DTableExportModel model) {
    Callable<StoreInfo> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return exportService.exportDTableDataAsXlsx(clientCtx, clientCtx.getCompany(), model);
    };
    return execute(Method.POST, "export-data", executor);
  }

  @ApiOperation(value = "Plugin Execute", response = PluginOutput.class)
  @PostMapping("plugin-execute")
  public @ResponseBody BackendResponse pluginExecute(HttpServletRequest httpReq, @RequestBody PluginExecuteModel model) {
    Callable<PluginOutput> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return service.pluginExecute(clientCtx, clientCtx.getCompany(), model);
    };
    return execute(Method.POST, "plugin-execute", executor);
  }

  @ApiOperation(value = "Find Available Plugins", responseContainer = "List", response = DTablePlugin.class)
  @GetMapping("plugin/find")
  public @ResponseBody BackendResponse findAvailablePlugins(HttpServletRequest httpReq) {
    Callable<List<DTablePlugin>> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return service.findAvailablePlugins(clientCtx, clientCtx.getCompany());
    };
    return execute(Method.GET, "plugin/find", executor);
  }

  @ApiOperation(value = "Upload Excel Data", responseContainer = "List", response = Map.class)
  @PostMapping("{code}/upload")
  public @ResponseBody BackendResponse upload(
      HttpServletRequest httpReq, @PathVariable("code") String code, @RequestBody UploadResource resource) {
    Callable<List<MapObject>> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return service.importXlsx(clientCtx, clientCtx.getCompany(), code, resource);
    };
    return execute(Method.POST, "upload", executor);
  }
}