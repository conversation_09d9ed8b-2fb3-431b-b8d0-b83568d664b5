package net.datatp.module.dtable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.xlsx.XLSXSectionWriter;
import net.datatp.module.data.xlsx.XLSXSheetWriter;
import net.datatp.module.data.xlsx.XLSXWorkbookWriter;
import net.datatp.module.data.xlsx.export.XLSXExportHelper;
import net.datatp.module.dtable.entity.DTable;
import net.datatp.module.dtable.entity.DTableCellType;
import net.datatp.module.dtable.entity.DTableColumn;
import net.datatp.module.dtable.entity.DTableColumnGroup;
import net.datatp.module.dtable.entity.DTableDynamicRow;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.http.get.GETContent;
import net.datatp.module.http.get.GETTmpStoreHandler;
import net.datatp.module.http.get.StoreInfo;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Service
public class DTableExportService {
  @Autowired
  private DTableLogic logic;
  
  @Autowired
  private GETTmpStoreHandler tmpStoreHandler;

  @Autowired
  private EmployeeLogic employeeLogic;
  
  //TODO: public StoreInfo exportSpreadsheetTemplateAsXlsx(ClientContext client, Company company, String spreadsheetCode)
  public StoreInfo exportDTableTemplateAsXlsx(ClientContext client, ICompany company, DTable DTable) {
    XLSXWorkbookWriter bookWriter = new XLSXWorkbookWriter();
    XLSXSheetWriter sheetWriter = bookWriter.createSheetWriter(DTable.getLabel());
    addHeadingWithGroupSection(client, bookWriter.getWorkbook(), sheetWriter, DTable);
    byte[] data = bookWriter.createByteArrayData();
    bookWriter.close();
    GETContent getContent = new GETContent(DTable.getCode() + ".xlsx", data);
    return tmpStoreHandler.store(client, getContent, true);
  }
  
  private Map<Integer, DTableColumn> addHeadingWithGroupSection(
    ClientContext client, Workbook workbook, XLSXSheetWriter sheetWriter, DTable DTable) {
    XLSXSectionWriter groupSection = sheetWriter.createSectionWriter();
    List<DTableColumnGroup> columnGroups = DTable.getColumnGroups();

    Map<Integer, DTableColumn> columnMap = new HashMap<>();

    Row groupRow = groupSection.nextRow();
    Row columnRow = groupSection.nextRow();
    XLSXExportHelper.setCellHeadingAsString(workbook, groupRow, 0, "_COMMENT_");
    XLSXExportHelper.setCellHeadingAsString(workbook, columnRow, 0, "No");
    int cellIdxTracker = 1;
    XLSXExportHelper.setCellHeadingAsString(workbook, groupRow, 1, "");
    XLSXExportHelper.setCellHeadingAsString(workbook, columnRow, 1, "Owner");
    cellIdxTracker++;
    sheetWriter.getSheet().setColumnWidth(0, 20 * 200);
    for (DTableColumnGroup group : columnGroups) {
      if(group.getColumns().size() > 1) {
        XLSXExportHelper.addHorizontalMergeCell(sheetWriter, groupRow, cellIdxTracker, cellIdxTracker + group.getColumns().size() - 1);
      }
      XLSXExportHelper.setCellHeadingAsString(workbook, groupRow, cellIdxTracker, group.getLabel());

      for(DTableColumn column: group.getColumns()) {
        columnMap.put(cellIdxTracker, column);
        XLSXExportHelper.setCellHeadingAsString(workbook, columnRow, cellIdxTracker, column.getLabel());
        cellIdxTracker++;
      }
    }
    for(int i = 1; i < cellIdxTracker; i++) {
      sheetWriter.getSheet().setColumnWidth(i, 20 * 256);
    }
    return columnMap;
  }
  
  public StoreInfo exportDTableDataAsXlsx(ClientContext client, ICompany company, DTableExportModel model) {
    List<DTableDynamicRow> rowData = model.getRecords();
    XLSXWorkbookWriter bookWriter = new XLSXWorkbookWriter();
    DTable DTable = logic.getDTableByCode(client, company, model.getSpreadsheetCode());
    Objects.assertNotNull(DTable, "Spreadsheet must be not null!");
    XLSXSheetWriter sheetWriter = bookWriter.createSheetWriter(DTable.getLabel());
    Map<Integer, DTableColumn> headerMapping = addHeadingWithoutGroupSection(client, bookWriter.getWorkbook(), sheetWriter, model);
    addContentSection(client, company, bookWriter.getWorkbook(), sheetWriter, headerMapping, rowData);
    byte[] data = bookWriter.createByteArrayData();
    bookWriter.close();
    GETContent getContent = new GETContent(DTable.getCode() + "-data.xlsx", data);
    return tmpStoreHandler.store(client, getContent, true);
  }

  private Map<Integer, DTableColumn> addHeadingWithoutGroupSection(
    ClientContext client, Workbook bookWriter, XLSXSheetWriter sheetWriter, DTableExportModel model) {
    XLSXSectionWriter groupSection = sheetWriter.createSectionWriter();
    Map<Integer, DTableColumn> columnMap = new HashMap<>();
    Row row = groupSection.nextRow();
    XLSXExportHelper.setCellHeadingAsString(bookWriter, row, 0, "No");
    int cellIdxTracker = 1;
    XLSXExportHelper.setCellHeadingAsString(bookWriter, row, 1, "Owner");
    cellIdxTracker++;
    sheetWriter.getSheet().setColumnWidth(0, 20 * 200);
    
    List<DTableColumn> exportColumns = model.getExportColumns();
    for (DTableColumn col : exportColumns) {
      int idx = col.getIdx() + 1; 
      columnMap.put(idx, col);
      XLSXExportHelper.setCellHeadingAsString(bookWriter, row, idx, col.getLabel());
      cellIdxTracker++;
    }
    
    for(int i = 1; i < cellIdxTracker; i++) {
      sheetWriter.getSheet().setColumnWidth(i, 20 * 256);
    }
    return columnMap;
  }

  private void addContentSection(
    ClientContext client, ICompany company, Workbook workbook, XLSXSheetWriter sheetWriter,
    Map<Integer, DTableColumn> headerMapping, List<DTableDynamicRow> rowData) {

    XLSXSectionWriter contentSection = sheetWriter.createSectionWriter();
    ArrayList<Integer> cellIdx = new ArrayList<>(headerMapping.keySet());
    CellStyle contentStyle = XLSXExportHelper.getContentStyle(workbook);
    CellStyle sttStyle = XLSXExportHelper.getContentStyle(workbook);

    for (DTableDynamicRow row : rowData) {
      Row nextRow = contentSection.nextRow();
      Cell sttCell = XLSXExportHelper.setCellAsString(nextRow, 0, String.valueOf(nextRow.getRowNum()));
      sttStyle.setAlignment(HorizontalAlignment.CENTER);
      sttCell.setCellStyle(sttStyle);
      StringBuilder empBuilder = new StringBuilder();
      final String ownerLabel = row.getOwnerLabel();
      empBuilder.append(ownerLabel);
      Cell cellOwner = XLSXExportHelper.setCellAsString(nextRow, 1, empBuilder.toString());
      cellOwner.setCellStyle(contentStyle);
      for(int idx: cellIdx) {
        DTableColumn column = headerMapping.get(idx);
        Object val = row.get(column.getColumnId());
        if(DTableCellType.Date.equals(column.getType()) && StringUtil.isNotEmpty((String) val)) {
          val = DateUtil.asCompactDate(DateUtil.parseCompactDate((String) val));
        }
        if(Objects.isNull(val)) val = StringUtil.EMPTY;
        Cell cell = XLSXExportHelper.setCellAsString(nextRow, idx, val.toString());
        cell.setCellStyle(contentStyle);
      }
    }
  }
}