package cloud.datatp.fforwarder.sales.integration;

import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.partner.BFSOnePartnerService;
import cloud.datatp.fforwarder.core.partner.PartnerNotificationTemplate;
import cloud.datatp.fforwarder.core.partner.SalemanPartnerObligationLogic;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartner;
import cloud.datatp.fforwarder.core.partner.entity.SalemanPartnerObligation;
import java.util.Date;
import java.util.List;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.AuthorizedToken;
import net.datatp.module.core.security.api.ApiAuthorizationPlugin;
import net.datatp.module.monitor.SourceType;
import net.datatp.module.monitor.call.DefaultEndpointCallResponse;
import net.datatp.module.monitor.call.EndpointCallContext;
import net.datatp.module.monitor.call.EndpointCallResponse;
import net.datatp.module.monitor.call.EndpointCallService;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SyncBFSOnePartnerCodeApiPlugin extends ApiAuthorizationPlugin<MapObject> {

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private EndpointCallService endpointCallService;

  @Autowired
  private BFSOnePartnerService bfsOnePartnerService;

  @Autowired
  private SalemanPartnerObligationLogic obligationLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private AccountLogic accountLogic;

  private static final String NAME = "resource:update-bfsone-partner-code-api";

  protected SyncBFSOnePartnerCodeApiPlugin() {
    super(NAME, "Update BFSOne Partner Code Api");
  }

  @Override
  public MapObject process(ClientContext client, AuthorizedToken token, MapObject params) {
    EndpointCallContext ctx = new EndpointCallContext(getClass(), this, "process") {
      protected void preCall() {
        withSourceType(SourceType.App);
      }

      @Override
      protected Object doCall() throws Throwable {

        MapObject result = new MapObject();

        Company company = companyLogic.getCompany(client, token.getCompanyId());
        DataSerializer.JSON.dump(company.getCode());
        DataSerializer.JSON.dump(params);

        String partnerCode = params.getString("newPartnerCode", "").trim();
        if(StringUtil.isEmpty(partnerCode)) {
          result.put("errorMessage", "New Partner code is empty!");
          return result;
        }

        String partnerCodeTemp = params.getString("partnerCodeTemp", "").trim();
        if(StringUtil.isEmpty(partnerCodeTemp)) {
          result.put("errorMessage", "Partner Code Temp is empty!");
          return result;
        }

        List<BFSOnePartner> partnerByCodeTemp = bfsOnePartnerService.findBFSOnePartnerByCodeTemp(client, company, partnerCodeTemp.trim());
        if (partnerByCodeTemp == null || partnerByCodeTemp.isEmpty()) {
          result.put("errorMessage", "No partner found with code temp: " + partnerCodeTemp);
          return result;
        }
        if (partnerByCodeTemp.size() > 1) {
          result.put("errorMessage", "Multiple partners found with code temp: " + partnerCodeTemp);
          return result;
        }
        BFSOnePartner partner = partnerByCodeTemp.get(0);
        String oldPartnerCode = partner.getBfsonePartnerCode();
        if(StringUtil.isNotEmpty(oldPartnerCode)) {
          List<SalemanPartnerObligation> obligations = obligationLogic.findByPartnerCode(client, oldPartnerCode);
          if(obligations != null && !obligations.isEmpty()) {
            for (SalemanPartnerObligation obligation : obligations) {
              obligation.setBfsonePartnerCode(partnerCode);
              obligation.setBfsonePartnerId(partner.getId());
              obligationLogic.updateObligation(client, obligation);
            }
          }
        }
        partner.setBfsonePartnerCode(partnerCode);
        partner.setPartnerCodeTemp(null);
        partner.setDateCreated(new Date());
        BFSOnePartner savedBFSOnePartner = bfsOnePartnerService.saveBFSOnePartner(client, partner);
        result.put("message", "Update BFSOne Partner Code successfully!");

        // Send notification with success/error message
        try {
            Account account = accountLogic.getAccountById(client, client.getAccountId());
            String updatedBy = account != null ? account.getFullName() : "System";
            
            // Build the Zalo message with success message
            String successMessage = result.getString("message");
            String errorMessage = result.getString("errorMessage");
            
            String zaloMessage = PartnerNotificationTemplate.buildPartnerCodeSyncZaloMessage(
                partner, 
                oldPartnerCode, 
                updatedBy,
                successMessage,
                errorMessage
            );
            
            // Create and send the message
            CRMMessageSystem message = BFSOnePartner.toApprovePartnerZaloMessage(zaloMessage);
            message.setReferenceId(savedBFSOnePartner.getId());
            crmMessageLogic.scheduleMessage(client, message);
        } catch (Exception e) {
            // Log error but don't fail the main operation
            System.err.println("Failed to send notification: " + e.getMessage());
            e.printStackTrace();
        }
        return result;
      }

    };

    EndpointCallResponse response = new DefaultEndpointCallResponse<MapObject>(ctx.getModule(), ctx.getComponentName(), ctx.getMethod());
    endpointCallService.call(ctx, response);
    return response.getDataAs(MapObject.class);
  }
}