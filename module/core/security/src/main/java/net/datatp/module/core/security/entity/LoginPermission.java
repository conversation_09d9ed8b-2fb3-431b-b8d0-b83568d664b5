package net.datatp.module.core.security.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.Persistable;
import net.datatp.security.client.Capability;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.MappedSuperclass;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;

@MappedSuperclass
@NoArgsConstructor @Getter @Setter
abstract public class LoginPermission extends Persistable<Long> {
  @Serial
  private static final long serialVersionUID = 1L;

  static public enum Type { Employee, Partner }

  @NotNull
  @Column(name="user_id")
  protected Long userId;

  protected String     label;

  @Enumerated(EnumType.STRING)
  protected Capability capability;

  @Enumerated(EnumType.STRING)
  protected Type type ;

  public LoginPermission(Long userId, String label, Capability cap) {
    this.userId     = userId;
    this.label      = label;
    this.capability = cap;
  }

  public boolean hasCapability(Capability requiredCap) {
    return capability.hasCapability(requiredCap);
  }
}