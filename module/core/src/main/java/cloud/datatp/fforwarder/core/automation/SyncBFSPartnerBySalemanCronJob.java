package cloud.datatp.fforwarder.core.automation;

import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.app.AppEnv;
import net.datatp.module.bot.BotEvent;
import net.datatp.module.bot.BotService;
import net.datatp.module.bot.cron.CronJob;
import net.datatp.module.bot.cron.CronJobFrequency;
import net.datatp.module.bot.cron.CronJobLogger;
import net.datatp.module.bot.task.TaskUnitBotEvent;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.company.CompanyReadLogic;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.monitor.SourceType;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DeviceInfo;
import net.datatp.security.client.DeviceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SyncBFSPartnerBySalemanCronJob extends CronJob {

  @Autowired
  private AppEnv appEnv;

  @Autowired
  private BotService botService;

  @Autowired
  private CompanyReadLogic companyLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private CommunicationMessageLogic communicationMessageLogic;

  public SyncBFSPartnerBySalemanCronJob() {
    super("datatp-crm:sync-bfsone-partner-by-saleman-daily", "Sync BFSOne Partner By Saleman Cron Job");
  }

  @PostConstruct
  public void onInit() {
    if (appEnv.isDevEnv()) {
      setFrequencies(CronJobFrequency.NONE);
    } else {
      setFrequencies(CronJobFrequency.EVERY_3_HOUR);
    }
  }

  @Override
  protected List<ICompany> getTargetCompanies() {
    List<ICompany> companies = new ArrayList<>();
    ICompany iCompany = ICompany.SYSTEM;
    String[] companyCodes = new String[]{"bee", "beehph", "beehan", "beehcm", "beedad"};
    for (String companyCode : companyCodes) {
      ICompany company = companyLogic.getCompany(getClientContext(iCompany), companyCode);
      companies.add(company);
    }
    return companies;
  }

  @Override
  protected ClientContext getClientContext(ICompany company) {
    ClientContext client = new ClientContext("default", "dan", "localhost");
    if (appEnv.isProdEnv()) {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Server));
    } else {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Computer));
    }
    client.setCompany(company);
    client.setAccountId(3L);
    return client;
  }

  protected Set<String> getReportToUsers(ClientContext client, ICompany company) {
    Set<String> userSet = super.getReportToUsers(client, company);
    userSet.add("dan");
    return userSet;
  }

  @Override
  protected void run(ClientContext client, ICompany company, CronJobLogger logger) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    log.info("\nRunning SyncBFSPartnerBySalemanCronJob for company: {}", client.getCompanyCode());

    ExecutableContext ctx =
      new ExecutableContext(client, client.getCompany())
        .withScriptEnv(
          scriptDir,
          SyncBFSPartnerSalemanLogicExecutor.class,
          SyncBFSPartnerSalemanLogicExecutor.FetchDataUserRole.class);

    BotEvent<?> botEvent =
      new TaskUnitBotEvent(client, company, ctx)
        .withProcessMode(BotEvent.ProcessMode.Queueable)
        .withReportToUsers(getReportToUsers(client, company));
    botService.broadcast(SourceType.UserBot, botEvent);
  }
}