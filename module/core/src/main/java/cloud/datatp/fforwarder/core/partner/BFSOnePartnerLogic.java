package cloud.datatp.fforwarder.core.partner;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartner;
import cloud.datatp.fforwarder.core.partner.entity.SalemanPartnerObligation;
import cloud.datatp.fforwarder.core.partner.repository.BFSOnePartnerRepository;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.NewAccountModel;
import net.datatp.module.account.ProfileLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.account.entity.AccountType;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.data.db.ExternalDataSourceManager;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.partner.PartnerLogic;
import net.datatp.module.resource.ResourceLogic;
import net.datatp.module.resource.entity.OwnerType;
import net.datatp.module.resource.location.CountryLogic;
import net.datatp.module.resource.location.entity.Country;
import net.datatp.module.resource.misc.ContactLogic;
import net.datatp.module.resource.misc.entity.AccountContact;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Component
public class BFSOnePartnerLogic extends CRMDaoService {

  @Autowired
  private BFSOnePartnerRepository fwdPartnerRepo;

  @Autowired
  private SalemanPartnerObligationLogic obligationLogic;

  @Autowired
  private PartnerLogic partnerLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private ProfileLogic profileLogic;

  @Autowired
  @Getter
  private ContactLogic contactLogic;

  @Autowired
  @Getter
  private ResourceLogic resourceLogic;

  @Autowired
  @Getter
  private CompanyLogic companyLogic;

  @Autowired
  private CountryLogic countryLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private ExecutableUnitManager executableUnitManager;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private ExternalDataSourceManager dataSourceManager;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  public BFSOnePartner getById(ClientContext client, Long id) {
    return fwdPartnerRepo.getById(id);
  }

  public BFSOnePartner getByAccountId(ClientContext client, Long accountId) {
    return fwdPartnerRepo.getByAccountId(accountId);
  }

  public BFSOnePartner saveBFSOnePartner(ClientContext client, BFSOnePartner partner) {
    partner.computePartnerGroup();
    partner.withBillReference();
    if (partner.isNew() || partner.getAccountId() == null) {
      try {
        Account account = getOrCreateAccount(client, partner);
        BFSOnePartner existed = getByAccountId(client, account.getId());
        if (existed != null) {
          existed.merge(partner);
          existed.set(client);
          return fwdPartnerRepo.save(existed);
        } else {
          partner.withAccount(account);
        }
      } catch(Exception e) {
        log.error("Error while creating BFSOnePartner: {}", partner.getBfsonePartnerCode(), e);
        throw new RuntimeException("Failed to create BFSOnePartner: " + partner.getBfsonePartnerCode(), e);
      }
    }
    partner.set(client);
    return fwdPartnerRepo.save(partner);
  }

  public List<BFSOnePartner> importPartners(ClientContext client, Employee saleman, List<BFSOnePartner> partners) {
    List<BFSOnePartner> holder = new ArrayList<>();
    try {
      for (BFSOnePartner partner : partners) {
        BFSOnePartner partnerInDb = getByCode(client, partner.getBfsonePartnerCode());
        if (partnerInDb == null) {
          partnerInDb = saveBFSOnePartner(client, partner);
        }
        obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(saleman, partnerInDb));
        holder.add(partnerInDb);
      }
    } catch (Exception e) {
      log.error("-------------------- Create Partners Error, Saleman: {}-------------------------", saleman.getLabel());
      throw new RuntimeException(e);
    }
    return holder;
  }

  public List<BFSOnePartner> importPartners(ClientContext client, CrmUserRole userRole, List<BFSOnePartner> partners) {
    List<BFSOnePartner> holder = new ArrayList<>();
    try {
      for (BFSOnePartner partner : partners) {
        BFSOnePartner partnerInDb = getByCode(client, partner.getBfsonePartnerCode());
        if (partnerInDb == null) {
          partnerInDb = saveBFSOnePartner(client, partner);
        }
        try {
          obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(userRole, partnerInDb));
        } catch (Exception e) {
          log.error("Failed to create SalemanPartnerObligation for userRole: {}, partner: {}", userRole.getFullName(), partner.getBfsonePartnerCode());
        }
        holder.add(partnerInDb);
      }
    } catch (Exception e) {
      log.error("-------------------- Create Partners Error, Saleman: {}-------------------------", userRole.getFullName());
      throw new RuntimeException(e);
    }
    return holder;
  }


  public List<BFSOnePartner> importPartners(ClientContext client, List<MapObject> records) {
    List<BFSOnePartner> holder = new ArrayList<>();
    try {
      log.info("Fetch saleman authorized data -------------------------\n\n\n\n");
      Map<String, List<BFSOnePartner>> salePartnerGroup = BFSOnePartner.groupBySaleObligation(records);
      for (String bfsoneCode : salePartnerGroup.keySet()) {
        List<BFSOnePartner> partners = salePartnerGroup.get(bfsoneCode);
        List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, bfsoneCode);
        if (Collections.isNotEmpty(employees) && employees.size() == 1) {
          Employee employee = employees.get(0);
          List<BFSOnePartner> savedPartners = importPartners(client, employee, partners);
          holder.addAll(savedPartners);
        } else {
          log.warn("Owner Saleman not found, code = {}", bfsoneCode);
        }
      }

      log.info("Fetch owner saleman partner data -------------------------\n\n\n\n");
      Map<String, List<BFSOnePartner>> firstSalePartnerGroup = BFSOnePartner.groupBySaleOwnerObligation(records);
      for (String bfsoneCode : firstSalePartnerGroup.keySet()) {
        List<BFSOnePartner> partners = firstSalePartnerGroup.get(bfsoneCode);
        List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, bfsoneCode);
        if (Collections.isNotEmpty(employees) && employees.size() == 1) {
          Employee employee = employees.get(0);
          List<BFSOnePartner> savedPartners = importPartners(client, employee, partners);
          holder.addAll(savedPartners);
        } else {
          log.warn("Owner Saleman not found, code = {}", bfsoneCode);
        }
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    return holder;
  }

  public List<BFSOnePartner> syncBFSOnePartnersBySaleman(ClientContext client, Long salemanAccountId) {
    Employee saleman = employeeLogic.getByAccount(client, null, salemanAccountId);
    boolean valid = saleman != null && StringUtil.isNotEmpty(saleman.getBfsoneUsername()) && StringUtil.isNotEmpty(saleman.getBfsoneCode());
    Objects.assertTrue(valid, "Saleman is not valid, account id = " + salemanAccountId);
    List<SqlMapRecord> records = searchBFSOnePartnersBySaleman(client, null, Arrays.asList(saleman));
    Map<String, List<BFSOnePartner>> allocateSalePartnerGroup = BFSOnePartner.groupBySaleObligation(records);
    Map<String, List<BFSOnePartner>> saleOwnerPartnerGroup = BFSOnePartner.groupBySaleOwnerObligation(records);
    List<BFSOnePartner> holder = new ArrayList<>();
    String bfsoneCode = saleman.getBfsoneCode();
    if (allocateSalePartnerGroup.containsKey(bfsoneCode)) {
      List<BFSOnePartner> bfsOnePartners = allocateSalePartnerGroup.get(bfsoneCode);
      holder.addAll(bfsOnePartners);
    }
    if (saleOwnerPartnerGroup.containsKey(bfsoneCode)) {
      List<BFSOnePartner> bfsOnePartners = saleOwnerPartnerGroup.get(bfsoneCode);
      holder.addAll(bfsOnePartners);
    }
    log.info("BFSOne Partners count = {}, Import for Saleman: {} ", holder.size(), saleman.getBfsoneUsername());
    return importPartners(client, saleman, holder);
  }

  public List<SqlMapRecord> searchBFSOnePartnersByModifiedTime(ClientContext client, Date fromTime, Date toTime) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/SearchBFSOnePartnerSql.groovy";
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.add(new RangeFilter("modifiedTime").withinValue(fromTime, toTime));
    return searchDbRecords(client, scriptDir, scriptFile, "SearchAllBFSOnePartnersByModifiedTime", sqlParams);
  }

  public List<SqlMapRecord> searchBFSOnePartners(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    try {
      sqlParams.addParam("accessAccountId", client.getAccountId());
      sqlParams.addParam("companyId", client.getCompanyId());
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/SearchBFSOnePartnerSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, "SearchBFSOnePartner", sqlParams);
    } catch (Exception e) {
      log.error("-------------------- Search BFSOne Partners Error -------------------------");
      log.error(e.getMessage());
      return java.util.Collections.emptyList();
    }
  }

  public List<SqlMapRecord> searchBFSOnePartnerCustomers(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    try {
      sqlParams.addParam("companyId", company.getId());
      sqlParams.addParam("accessAccountId", client.getAccountId());
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/SearchBFSOnePartnerCustomerSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, "SearchBFSOnePartner", sqlParams);
    } catch (Exception e) {
      log.error("-------------------- Search BFSOne Partners Error -------------------------");
      log.error(e.getMessage());
      return java.util.Collections.emptyList();
    }
  }

  public int deleteBFSOnePartner(ClientContext client, ICompany company, List<Long> targetIds) {
    DBConnectionUtil connectionUtil = getDBConnectionUtil();
    DeleteGraphBuilder deleteGraphBuilder = new DeleteGraphBuilder(connectionUtil, company.getId(), BFSOnePartner.class, targetIds);
    final int count = deleteGraphBuilder.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count;
  }

  public List<BFSOnePartner> findByTaxCode(ClientContext client, ICompany company, String taxCode) {
    return fwdPartnerRepo.findByTaxCode(taxCode);
  }

  public BFSOnePartner getByCode(ClientContext client, String code) {
    return fwdPartnerRepo.getByBFSOneCode(code);
  }

  public List<BFSOnePartner> findByBFSOneCodeTemp(ClientContext client, String codeTemp) {
    return fwdPartnerRepo.findByBFSOneCodeTemp(codeTemp);
  }

  public Account getOrCreateAccount(ClientContext client, BFSOnePartner record) {
    String legacyLoginId = record.getBfsonePartnerCode();
    Account account = accountLogic.getAccountRepo().getByLegacyLoginId(legacyLoginId);
    if (account != null) return account;
    log.info("--- Create account: bfsone code = " + legacyLoginId);
    account = new Account();
    account.setLoginId(legacyLoginId);
    account.setAccountType(AccountType.ORGANIZATION);
    account.setFullName(record.getLabel());
    account.setLegacyLoginId(legacyLoginId);
    NewAccountModel model = accountLogic.createNewAccount(client, new NewAccountModel(account));
    account = model.getAccount();

    AccountContact contact = new AccountContact("Contact");
    List<Country> countries = countryLogic.findCountriesByPattern(client, record.getCountryLabel());
    if (Collections.isNotEmpty(countries)) {
      final Country country = countries.get(0);
      contact.setCountryName(country.getCode());
      contact.setCountryLabel(country.getLabel());
    }
    contact.setEmail(Arrays.asSet(account.getEmail()));
    contact.setMobile(Arrays.asSet(account.getMobile()));
    contact.setOwnerType(OwnerType.Account);
    contact.setOwnerId(account.getId());
    contact.setOwnerIdentifier(account.getLoginId());
    contactLogic.saveContact(client, contact);
    return account;
  }

  @SuppressWarnings("unchecked")
  public List<SqlMapRecord> searchBFSOnePartnersBySaleman(ClientContext client, ICompany company, List<Employee> employees) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("contactIds", employees.stream()
      .map(Employee::getBfsoneCode)
      .filter(code -> code != null && !code.isEmpty())
      .collect(Collectors.toList()));

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/fforwarder/core/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePartnerBySaleman")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  public List<SqlMapRecord> searchBFSOnePartnersByContactIds(ClientContext client, ICompany company, List<String> contactIds) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("contactIds", contactIds);
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/fforwarder/core/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePartnerBySaleman")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  @SuppressWarnings("unchecked")
  public List<SqlMapRecord> searchPublicBFSOnePartners(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/fforwarder/core/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePublicPartner")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  @SuppressWarnings("unchecked")
  public BFSOnePartner fetchBFSOnePartnersByCode(ClientContext client, ICompany company, String bfsonePartnerCode) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("bfsonePartnerCode", bfsonePartnerCode);

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/fforwarder/core/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePartnerBySaleman")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);

    List<SqlMapRecord> records = (List<SqlMapRecord>) executableUnitManager.execute(ctx);
    if (Collections.isNotEmpty(records)) {
      SqlMapRecord record = records.get(0);
      return new BFSOnePartner(record);
    }
    return null;
  }

  @SuppressWarnings("unchecked")
  public List<BFSOnePartner> fetchBFSOnePartners(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/fforwarder/core/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePartnerBySaleman")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);
    List<BFSOnePartner> partners = new ArrayList<>();
    List<SqlMapRecord> records = (List<SqlMapRecord>) executableUnitManager.execute(ctx);
    for (SqlMapRecord record : records) {
      partners.add(new BFSOnePartner(record));
    }
    return partners;
  }

  public List<SqlMapRecord> checkBFSOnePartnerByTaxCode(ClientContext client, String taxCode) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("taxCode", taxCode.trim());
    Objects.assertNotNull(taxCode, "Tax code must be required!!!");
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/fforwarder/core/partner/BFSOnePartnerLogicUnit.java", "CheckBFSOnePartnerByTaxCode")
        .withParam(this).withParam(client).withParam(sqlParams);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  public BFSOnePartner syncBFSOnePartnersByCode(ClientContext client, ICompany company, String bfsonePartnerCode) {
    BFSOnePartner bfsOnePartner = fetchBFSOnePartnersByCode(client, company, bfsonePartnerCode);
    if (bfsOnePartner != null) {
      BFSOnePartner partnerInDb = getByCode(client, bfsOnePartner.getBfsonePartnerCode());
      if (partnerInDb == null) {
        partnerInDb = saveBFSOnePartner(client, bfsOnePartner);
      }

      String salemanObligationCode = bfsOnePartner.getSalemanObligationCode();
      if (StringUtil.isNotEmpty(salemanObligationCode)) {
        List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, salemanObligationCode);
        if (Collections.isNotEmpty(employees) && employees.size() == 1) {
          Employee saleman = employees.get(0);
          obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(saleman, partnerInDb));
        }
      }

      String saleOwnerContactCode = bfsOnePartner.getSaleOwnerContactCode();
      if (StringUtil.isNotEmpty(saleOwnerContactCode)) {
        List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, saleOwnerContactCode);
        if (Collections.isNotEmpty(employees) && employees.size() == 1) {
          Employee saleman = employees.get(0);
          obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(saleman, partnerInDb));
        }
      }

      // Send notification for partner sync
      try {
        Account account = accountLogic.getAccountById(client, client.getAccountId());
        String updatedBy = account != null ? account.getFullName() : "System";
        boolean isNewPartner = (partnerInDb.getDateCreated().getTime() > System.currentTimeMillis() - 60000); // Within 1 minute
        String zaloMessage = PartnerNotificationTemplate.buildPartnerSyncZaloMessage(partnerInDb, updatedBy, isNewPartner);
        CRMMessageSystem message = BFSOnePartner.toApprovePartnerZaloMessage(zaloMessage);
        message.setReferenceId(partnerInDb.getId());
        crmMessageLogic.scheduleMessage(client, message);
      } catch (Exception e) {
        System.err.println("Failed to send partner sync notification: " + e.getMessage());
        e.printStackTrace();
      }

      return partnerInDb;
    }
    return null;
  }

}