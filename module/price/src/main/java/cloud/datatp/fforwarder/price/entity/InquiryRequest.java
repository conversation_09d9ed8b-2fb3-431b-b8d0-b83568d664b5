package cloud.datatp.fforwarder.price.entity;

import cloud.datatp.fforwarder.core.common.Purpose;
import cloud.datatp.fforwarder.core.common.TransportationMode;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.price.common.ClientPartnerType;
import cloud.datatp.fforwarder.price.common.ShipmentDetail;
import cloud.datatp.fforwarder.price.request.InquiryOverDueReminderMessagePlugin;
import cloud.datatp.fforwarder.price.request.InquiryRequestNotificationTemplate;
import cloud.datatp.fforwarder.price.request.InquiryUpdatedMessagePlugin;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.entity.Account;
import net.datatp.module.communication.AttachmentResource;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.security.client.ClientContext;
import net.datatp.util.bean.BeanUtil;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Slf4j
@Entity
@Table(
  name = InquiryRequest.TABLE_NAME,
  indexes = {
    @Index(columnList = "code"),
    @Index(columnList = "status"),
    @Index(columnList = "request_date"),
    @Index(columnList = "pricing_date"),
    @Index(columnList = "saleman_account_id"),
    @Index(columnList = "pricing_account_id"),
    @Index(name = "idx_inquiry_location", columnList = "from_location_code, to_location_code"),
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class InquiryRequest extends CompanyEntity {

  @Serial
  private static final long serialVersionUID = 1L;
  public static final String TABLE_NAME = "lgc_price_inquiry_request";
  public static final String SEQUENCE = "lgc:lgc_price_inquiry_request";

  public enum InquiryStatus {
    SUCCESS, PRICE_MISMATCH, NO_RESPONSE, RESPONDED, DONE, IN_PROGRESS, REJECTED;

    static public InquiryStatus parse(String token) {
      if (StringUtil.isEmpty(token)) return IN_PROGRESS;
      if (SUCCESS.toString().equalsIgnoreCase(token.trim())) return SUCCESS;
      if (PRICE_MISMATCH.toString().equalsIgnoreCase(token.trim())) return PRICE_MISMATCH;
      if (NO_RESPONSE.toString().equalsIgnoreCase(token.trim())) return NO_RESPONSE;
      if (RESPONDED.toString().equalsIgnoreCase(token.trim())) return RESPONDED;
      if (DONE.toString().equalsIgnoreCase(token.trim())) return DONE;
      if (REJECTED.toString().equalsIgnoreCase(token.trim())) return REJECTED;
      return IN_PROGRESS;
    }

    public boolean isChecking() {
      return this == IN_PROGRESS;
    }

    public String getLabel() {
      return switch (this) {
        case SUCCESS -> "Win";
        case RESPONDED -> "Responded";
        case PRICE_MISMATCH -> "Mismatch";
        case NO_RESPONSE -> "No Response";
        case DONE -> "Done";
        case IN_PROGRESS -> "Checking";
        case REJECTED -> "Reject";
      };
    }
  }

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "request_date", nullable = false)
  private Date requestDate = new Date();

  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false)
  private InquiryStatus status = InquiryStatus.IN_PROGRESS;

  @Column(name = "code", nullable = false, length = 50)
  private String code;

  @Enumerated(EnumType.STRING)
  @Column(name = "mode", nullable = false)
  private TransportationMode mode;

  @Enumerated(EnumType.STRING)
  @Column(name = "purpose")
  private Purpose purpose;

  @Column(name = "type_of_shipment")
  private String typeOfShipment;

  /* -------------- Customer/ Agent/ Customer Lead ------------------- */
  @Enumerated(EnumType.STRING)
  @Column(name = "client_partner_type")
  private ClientPartnerType clientPartnerType = ClientPartnerType.CUSTOMERS;

  @Column(name = "client_partner_id")
  private Long clientPartnerId;

  @Column(name = "client_label")
  private String clientLabel;

  /* -------------- Saleman------------------- */
  @Column(name = "saleman_account_id")
  private Long salemanAccountId;

  @Column(name = "saleman_label")
  private String salemanLabel;

  @Column(name = "saleman_email")
  private String salemanEmail;

  @Column(name = "saleman_phone")
  private String salemanPhone;

  @Column(name = "saleman_job_title")
  private String salemanJobTitle;

  @Column(name = "saleman_branch_name")
  private String salemanBranchName;

  /* -------------- Pricing------------------- */
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "pricing_date")
  private Date pricingDate;

  @Column(name = "pricing_account_id")
  private Long pricingAccountId;

  @Column(name = "pricing_label")
  private String pricingLabel;

  @Column(name = "term_of_service")
  private String termOfService;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "cargo_ready_date")
  private Date cargoReadyDate;

  @Column(name = "is_multi_route")
  private boolean isMultiRoute;

  @Column(name = "from_location_code")
  private String fromLocationCode;

  @Column(name = "from_location_label")
  private String fromLocationLabel;

  @Column(name = "to_location_code")
  private String toLocationCode;

  @Column(name = "to_location_label")
  private String toLocationLabel;

  @Column(name = "pickup_address", length = 2 * 1024)
  private String pickupAddress;

  @Column(name = "delivery_address", length = 2 * 1024)
  private String deliveryAddress;

  @Column(name = "target_rate", length = 2 * 1024)
  private String targetRate;

  @Column(name = "note", length = 2 * 1024)
  private String note;

  @Column(name = "feedback", length = 2 * 1024)
  private String feedback;

  @Column(name = "pricing_note", length = 2 * 1024)
  private String pricingNote;

  @Column(name = "total_new_prices_count")
  private Long totalNewPricesCount;

  @Column(name = "total_analysis_prices_count")
  private Long totalAnalysisPricesCount;

  @Column(name = "step_tracking")
  private String stepTracking;

  @Column(name = "total_step_counting")
  private Long totalStepCounting;

  @Transient
  private MapObject jobTrackingStatus = new MapObject();

  @JsonIgnore
  @Access(AccessType.PROPERTY)
  @Basic(fetch = FetchType.EAGER)
  @Column(name = "job_tracking_status", length = 64 * 1024)
  public String getJobTrackingStatusJson() {
    if (this.jobTrackingStatus == null) return null;
    return DataSerializer.JSON.toString(this.jobTrackingStatus);
  }

  public void setJobTrackingStatusJson(String json) {
    if (StringUtil.isEmpty(json)) {
      this.jobTrackingStatus = null;
    } else {
      this.jobTrackingStatus = DataSerializer.JSON.fromString(json, new TypeReference<>() {
      });
    }
  }

  // ----------- For job tracking fields ------------------
  @Embedded
  private ShipmentDetail shipmentDetail = new ShipmentDetail();

  public InquiryRequest withPricingAccount(Account account) {
    if (pricingDate == null) this.pricingDate = new Date();
    this.pricingAccountId = account.getId();
    this.pricingLabel = account.getFullName();
    return this;
  }

  public InquiryRequest computeVolumeReport() {
    if (shipmentDetail != null) {
      double grossWeightKg = shipmentDetail.getGrossWeightKg();
      double volumeCbm = shipmentDetail.getVolumeCbm();
      String volumeInfo = shipmentDetail.getVolumeInfo();
      if (TransportationMode.isSeaFCLTransport(mode)) {
        shipmentDetail.setReportVolumeUnit("TEU");
        double tueReport = ShipmentDetail.calculateTUE(volumeInfo);
        shipmentDetail.setReportVolume(tueReport);
      } else if (TransportationMode.isSeaLCLTransport(mode)) {
        shipmentDetail.setReportVolumeUnit("CBM");
        double gwInTon = 0;
        if (grossWeightKg > 0) gwInTon = grossWeightKg / 1000;
        double reportVolume = Math.max(gwInTon, volumeCbm);
        shipmentDetail.setReportVolume(reportVolume);
      } else if (TransportationMode.isAirTransport(mode)) {
        shipmentDetail.setReportVolumeUnit("KGS");
        //double reportVolume = Math.max(grossWeightKg, volumeCbm * 166.67);
        shipmentDetail.setReportVolume(grossWeightKg);
      }
    }
    return this;
  }

  @Transient
  @Column(name = "mail_message", length = 32 * 1024)
  private String mailMessage;

  @Column(name = "mail_subject", length = 32 * 1024)
  private String mailSubject;

  @Column(name = "mail_to")
  private String to;

  @Column(name = "mail_cc")
  private String cc;

  @Transient
  List<AttachmentResource> attachments = new ArrayList<>();

  public ClientPartnerType getClientPartnerType() {
    if(clientPartnerType == null) clientPartnerType = ClientPartnerType.CUSTOMERS;
    return clientPartnerType;
  }

  public void setClientPartnerType(ClientPartnerType type) {
    if(type == null) type = ClientPartnerType.CUSTOMERS;
    this.clientPartnerType = type;
  }

  public InquiryRequest computeFromMapObject(MapObject sel) {
    List<String> fields = List.of(
      "requestDate", "typeOfShipment", "feedback",
      "clientPartnerId", "clientLabel",
      "salemanAccountId", "salemanLabel", "salemanEmail", "salemanPhone",
      "pricingAccountId", "pricingLabel", "pricingDate",
      "termOfService", "cargoReadyDate",
      "fromLocationCode", "fromLocationLabel", "toLocationCode",
      "toLocationLabel", "pickupAddress", "deliveryAddress",
      "targetRate", "note", "pricingNote",
      "totalNewPricesCount", "totalAnalysisPricesCount",
      "stepTracking", "totalStepCounting", "mailSubject"
    );

    List<String> shipmentFields = List.of(
      "dimensionL", "dimensionW", "dimensionH", "volumeInfo", "packageQty", "commodity",
      "descOfGoods", "volumeCbm", "grossWeightKg", "dgLiquidCargo",
      "buyInsuranceRequest", "specialRequestNote", "freeTimeTerminalRequest",
      "vnBorderGateRequest", "expressCourier", "crossBorderTrucking"
    );

    BeanUtil.updateFieldsFromMap(this, sel, fields);
    this.shipmentDetail = Objects.ensureNotNull(shipmentDetail, ShipmentDetail::new);
    BeanUtil.updateFieldsFromMap(this.shipmentDetail, sel, shipmentFields);

    if (sel.containsKey("stackable")) {
      String stackableStr = sel.getString("stackable", null);
      if (!StringUtil.isEmpty(stackableStr)) {
        this.shipmentDetail.setStackable(ShipmentDetail.Stackable.valueOf(stackableStr));
      }
    }

    if (sel.containsKey("mode")) {
      String modeStr = sel.getString("mode", null);
      if (!StringUtil.isEmpty(modeStr)) {
        this.mode = TransportationMode.parse(modeStr);
      }
    }

    if (sel.containsKey("purpose")) {
      String purposeStr = sel.getString("purpose", null);
      if (!StringUtil.isEmpty(purposeStr)) {
        this.purpose = Purpose.parse(purposeStr);
      }
    }

    if (sel.containsKey("status")) {
      String pricingStatusStr = sel.getString("status", null);
      if (!StringUtil.isEmpty(pricingStatusStr)) {
        this.status = InquiryStatus.parse(pricingStatusStr);
      }
    }

    if (sel.containsKey("lastStepName")) {
      this.stepTracking = sel.getString("lastStepName", null);
    }

    if (sel.containsKey("stepDoneCount")) {
      this.totalStepCounting = sel.getLong("stepDoneCount", null);
    }


    if (sel.containsKey("jobTrackingStatus")) {
      try {
        this.jobTrackingStatus = sel.getMapObject("jobTrackingStatus");
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        this.jobTrackingStatus = null;
      }
    }

    this.computeVolumeReport();
    return this;
  }

  public void computeMailSubject() {
    if (StringUtil.isNotEmpty(mailSubject)) return;

    boolean isCBT = shipmentDetail.isCrossBorderTrucking();
    boolean isExpressCourier = shipmentDetail.isExpressCourier();

    String typeOfService = "";
    if (TransportationMode.isAirTransport(this.mode) && isExpressCourier) {
      typeOfService = "CPN";
    } else if (TransportationMode.isAirTransport(this.mode) && !isExpressCourier) {
      typeOfService = "AIR";
    } else if (TransportationMode.isSeaLCLTransport(this.mode)) {
      typeOfService = "LCL";
    } else if (TransportationMode.isSeaFCLTransport(this.mode)) {
      typeOfService = "FCL";
    } else if (TransportationMode.isTruckTransport(this.mode) && isCBT) {
      typeOfService = "CBT";
    } else if (TransportationMode.isTruckTransport(this.mode) && !isCBT) {
      typeOfService = "LOGISTICS";
    } else if (TransportationMode.isRailTransport(this.mode)) {
      typeOfService = "RAIL";
    }

    if (StringUtil.isEmpty(termOfService)) termOfService = "[INCOTERMS]";
    if (StringUtil.isEmpty(fromLocationLabel)) fromLocationLabel = "[POL]";
    if (StringUtil.isEmpty(toLocationLabel)) toLocationLabel = "[POD]";
    StringBuilder builder = new StringBuilder();
    builder.append("REF:").append(code);
    builder.append("/ ");
    builder.append(typeOfService).append(" ");
    builder.append(termOfService).append(" ");
    builder.append(fromLocationLabel).append(" ");
    builder.append("TO").append(" ");
    builder.append(toLocationLabel).append(" ");

    if (StringUtil.isNotEmpty(clientLabel)) {
      builder.append(" - ").append(clientLabel);
    }
    mailSubject = builder.toString();
  }

  public void setTo(String text) {
    if (text == null) {
      this.to = null;
    } else {
      text = text.toLowerCase();
      Set<String> variantSet = StringUtil.toStringHashSet(text);
      this.to = StringUtil.join(variantSet);
    }
  }

  public void withTo(String text) {
    if (StringUtil.isEmpty(to)) {
      setTo(text);
    } else {
      text = text.toLowerCase();
      Set<String> origin = StringUtil.toStringHashSet(to);
      Set<String> variantSet = StringUtil.toStringHashSet(text);
      origin.addAll(variantSet);
      setTo(new ArrayList<>(origin));
    }
  }

  @JsonGetter("to")
  public List<String> getToList() {
    if (to == null || to.isEmpty()) return new ArrayList<>();
    return Arrays.stream(to.split(","))
      .map(String::trim)
      .collect(Collectors.toList());
  }

  @JsonSetter("to")
  public void setTo(List<String> toList) {
    if (toList == null || toList.isEmpty()) this.to = null;
    else this.to = toList.stream().map(String::trim).collect(Collectors.joining(","));
  }

  public void setCc(String text) {
    if (text == null) {
      this.cc = null;
    } else {
      text = text.toLowerCase();
      Set<String> variantSet = StringUtil.toStringHashSet(text);
      this.cc = StringUtil.join(variantSet);
    }
  }

  @JsonGetter("cc")
  public List<String> getCcList() {
    if (cc == null || cc.isEmpty()) return new ArrayList<>();
    return Arrays.stream(cc.split(","))
      .map(String::trim)
      .collect(Collectors.toList());
  }

  @JsonSetter("cc")
  public void setCc(List<String> ccList) {
    if (ccList == null || ccList.isEmpty()) this.cc = null;
    else this.cc = ccList.stream().map(String::trim).collect(Collectors.joining(","));
  }
  // -------------- mail detail  ----------------

  public CRMMessageSystem toSalemanChangeStatusMessage(ClientContext client) {
    Objects.assertNotNull(!isNew(), "New inquiry request cannot be send to saleman change status message");
    CRMMessageSystem message = new CRMMessageSystem();
    message.setContent(InquiryRequestNotificationTemplate.buildMailMessage(this, client.getRemoteUser()));
    message.setScheduledAt(new Date());
    message.setMessageType(MessageType.MAIL);
    message.setReferenceId(id);
    message.setReferenceType(InquiryRequest.TABLE_NAME);
    message.setPluginName(InquiryUpdatedMessagePlugin.PLUGIN_TYPE);
    message.setRecipients(new HashSet<>(getToList()));
    MapObject metadata = new MapObject();
    metadata.put("fromEmail", "<EMAIL>");
    metadata.put("subject", String.format("CRM - Inquiry Request [REF: %s - %s]", code, status.getLabel()));
    metadata.put("to", getToList());
    metadata.put("ccList", getCcList());
    message.setMetadata(metadata);
    return message;
  }

  public CRMMessageSystem toPricingRejectMessage(ClientContext client, String pricingEmail) {
    Objects.assertNotNull(!isNew(), "New inquiry request cannot be send to saleman change status message");
    CRMMessageSystem message = new CRMMessageSystem();
    message.setContent(InquiryRequestNotificationTemplate.buildMailMessage(this, client.getRemoteUser()));
    message.setScheduledAt(new Date());
    message.setMessageType(MessageType.MAIL);
    message.setReferenceId(id);
    message.setReferenceType(InquiryRequest.TABLE_NAME);
    message.setPluginName(InquiryUpdatedMessagePlugin.PLUGIN_TYPE);
    message.setRecipients(new HashSet<>(java.util.Collections.singletonList(salemanEmail)));
    MapObject metadata = new MapObject();
    metadata.put("fromEmail", pricingEmail);
    metadata.put("subject", String.format("CRM - Inquiry Request [REF: %s - %s]", code, InquiryStatus.REJECTED.getLabel()));
    metadata.put("to", java.util.Collections.singletonList(salemanEmail));
    metadata.put("ccList", getCcList());
    message.setMetadata(metadata);
    return message;
  }

  public CRMMessageSystem toOverDueZaloMessage(ClientContext client) {
    Objects.assertNotNull(!isNew(), "New inquiry request cannot be send to saleman change status message");
    Objects.assertTrue(StringUtil.isNotBlank(salemanPhone), "Inquiry request has no saleman phone");

    Calendar calendar = Calendar.getInstance();
    calendar.setTime(pricingDate);
    calendar.add(Calendar.HOUR, 24);

    if(calendar.before(new Date())) {
      calendar.setTime(new Date());
    }

    CRMMessageSystem message = new CRMMessageSystem();
    message.setContent(InquiryRequestNotificationTemplate.buildUncompletedInquiryZaloMessage(this));
    message.setScheduledAt(calendar.getTime());
    message.setMessageType(MessageType.ZALO);
    message.setReferenceId(id);
    message.setReferenceType(InquiryRequest.TABLE_NAME);
    message.setPluginName(InquiryOverDueReminderMessagePlugin.PLUGIN_TYPE);
    message.setRecipients(new HashSet<>(Collections.singletonList(salemanPhone)));
    return message;
  }

  public CRMMessageSystem toOverDueMailMessage(ClientContext client, int reminderCount) {
    Objects.assertNotNull(!isNew(), "New inquiry request cannot be send to saleman change status message");
    Objects.assertTrue(StringUtil.isNotBlank(salemanEmail), "Inquiry request has no saleman email");

    this.status = InquiryStatus.NO_RESPONSE;

    Calendar calendar = Calendar.getInstance();
    calendar.setTime(pricingDate);
    calendar.add(Calendar.HOUR, 24);

    if(calendar.before(new Date())) {
      calendar.setTime(new Date());
    }

    CRMMessageSystem message = new CRMMessageSystem();
    message.setContent(InquiryRequestNotificationTemplate.buildInquiryDetailMailMessage(this));
    message.setScheduledAt(calendar.getTime());
    message.setMessageType(MessageType.MAIL);
    message.setReferenceId(id);
    message.setReferenceType(InquiryRequest.TABLE_NAME);
    message.setPluginName(InquiryOverDueReminderMessagePlugin.PLUGIN_TYPE);
    message.setRecipients(new HashSet<>(java.util.Collections.singletonList(salemanEmail)));

    MapObject metadata = new MapObject();
    metadata.put("fromEmail", "<EMAIL>");
    metadata.put("subject", String.format("CRM - ⚠️ Alert: Overdue Inquiry Request [%s] - Reminder %s", mailSubject, reminderCount));
    metadata.put("ccList", getToList());
    message.setMetadata(metadata);
    return message;
  }


}