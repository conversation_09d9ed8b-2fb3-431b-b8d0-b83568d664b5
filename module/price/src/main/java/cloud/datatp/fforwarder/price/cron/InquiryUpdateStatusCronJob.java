package cloud.datatp.fforwarder.price.cron;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.app.AppEnv;
import net.datatp.module.bot.BotEvent;
import net.datatp.module.bot.BotService;
import net.datatp.module.bot.cron.CronJob;
import net.datatp.module.bot.cron.CronJobFrequency;
import net.datatp.module.bot.cron.CronJobLogger;
import net.datatp.module.bot.task.TaskUnitBotEvent;
import net.datatp.module.company.CompanyReadLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.monitor.SourceType;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DeviceInfo;
import net.datatp.security.client.DeviceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class InquiryUpdateStatusCronJob extends CronJob {

  @Autowired
  private AppEnv appEnv;

  @Autowired
  private BotService botService;

  @Autowired
  private CompanyReadLogic companyLogic;

  protected InquiryUpdateStatusCronJob() {
    super("price:inquiry-update-to-no-response-status:daily", "Inquiry Update To No Response Status Cron Job");
  }

  @PostConstruct
  public void onInit() {
    if (appEnv.isDevEnv()) {
      setFrequencies(CronJobFrequency.NONE);
    } else {
      //setFrequencies(CronJobFrequency.EVERY_DAY_08_AM);
      setFrequencies(CronJobFrequency.EVERY_DAY_08_AM, CronJobFrequency.EVERY_DAY_14_PM);
    }
  }

  @Override
  protected List<ICompany> getTargetCompanies() {
    List<ICompany> companies = new ArrayList<>();
    ICompany iCompany = ICompany.SYSTEM;
    String[] companyCodes = new String[]{"bee", "beehph", "beehan", "beehcm", "beedad"};
    for (String companyCode : companyCodes) {
      Company company = companyLogic.getCompany(getClientContext(iCompany), companyCode);
      companies.add(company);
    }
    return companies;
  }

  @Override
  protected ClientContext getClientContext(ICompany company) {
    ClientContext client = new ClientContext("default", "dan", "localhost");
    if (appEnv.isProdEnv()) {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Server));
    } else {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Computer));
    }
    client.setAccountId(3L);
    client.setCompany(company);
    return client;
  }

  protected Set<String> getReportToUsers(ClientContext client, ICompany company) {
    Set<String> userSet = super.getReportToUsers(client, company);
    userSet.add("dan");
    return userSet;
  }

  @Override
  protected void run(ClientContext client, ICompany company, CronJobLogger logger) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");

    ExecutableContext ctx =
        new ExecutableContext(client, company)
            .withScriptEnv(
                scriptDir,
              InquiryAutomationExecutor.class,
              InquiryAutomationExecutor.UpdateToNoResponseStatus.class);

    BotEvent<?> botEvent =
        new TaskUnitBotEvent(client, company, ctx)
            .withProcessMode(BotEvent.ProcessMode.Immediately)
            .withReportToUsers(getReportToUsers(client, company));

    botService.broadcast(SourceType.UserBot, botEvent);

  }
}